# frozen_string_literal: true

class AddStatusToRoles < ActiveRecord::Migration[7.2]
  def up
    create_enum :role_status, %w[
      active
      inactive
      uninvited
      invited
    ]

    add_column :roles, :status, :enum, enum_type: :role_status, default: "active", null: false

    # Update existing roles to have the correct status
    execute <<-SQL
      UPDATE roles SET status = 'inactive' WHERE active = false;
    SQL
  end

  def down
    remove_column :roles, :status
    execute <<-SQL
      DROP TYPE role_status;
    SQL
  end
end
