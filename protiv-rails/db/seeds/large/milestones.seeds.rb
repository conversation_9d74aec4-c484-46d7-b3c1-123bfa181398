# frozen_string_literal: true

after "large:jobs" do
  return if Milestone.any?

  milestones = []

  job_count = Job.count
  Job.all.each_with_index do |job, idx|
    SEED_LOGGER.info("Milestones for job #{idx + 1}/#{job_count}")
    (1..((idx % 2) + 1)).each do |i|
      milestones << Milestone.new(status: "pending", job: job,
                                  seconds_budget: 1519200,
                                  seconds_cost: 1519200,
                                  labor_budget_cents: 717400,
                                  currency: "USD")
    end
  end
  Milestone.import(milestones)
end
