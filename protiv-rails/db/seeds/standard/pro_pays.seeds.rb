# frozen_string_literal: true

after "standard:jobs" do
  return if SubProPay.any?
  Job.limit(4).each_with_index do |job, idx|
    if ((idx + 1) % 2) == 1
      job.milestones.each do |ms|
        pro_pay = ProPay.create!(organization: job.organization, source_type: "manual")
        sub_pro_pay = SubProPay.create!(pro_pay: pro_pay,
                                        distribution_type: "equal_rate",
                                        budget_type: "hours",
                                        budget_minutes: 143 * 60)

        SubProPayProPayable.create!(sub_pro_pay: sub_pro_pay, pro_payable: ms)

        partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay
        partial_sub_pro_pay.update!(estimated_percent_complete: 100)
        partial_sub_pro_pay.materialize!
      end
    else
      pro_pay = ProPay.create!(organization: job.organization, source_type: "manual")

      sub_pro_pay = SubProPay.create!(pro_pay: pro_pay,
                                      distribution_type: "equal_rate",
                                      budget_type: "hours",
                                      budget_minutes: 143 * 60 * 2) # FIXME: add up the milestone budgets

      SubProPayProPayable.create!(sub_pro_pay: sub_pro_pay, pro_payable: job)

      partial_sub_pro_pay = sub_pro_pay.find_or_create_open_partial_sub_pro_pay
      partial_sub_pro_pay.update!(estimated_percent_complete: 100)
      partial_sub_pro_pay.materialize!
    end
  end
end
