# frozen_string_literal: true

after "standard:jobs" do
  return if Milestone.any?

  milestones = []

  Job.all.each_with_index do |job, idx|
    (1..((idx % 2) + 1)).each do |i|
      milestones << Milestone.new(status: "pending", job: job,
                                  seconds_budget: 1519200,
                                  seconds_cost: 1519200,
                                  labor_budget_cents: 717400,
                                  currency: "USD")
    end
  end
  Milestone.import(milestones)
end
