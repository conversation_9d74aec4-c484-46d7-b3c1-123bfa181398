# frozen_string_literal: true

after "standard:routes" do
  Branch.all.each do |branch|
    next if branch.jobs.any?

    jobs = []

    (1..10).each do |i|
      jobs << Job.new(
        name: "Job #{i}",
        job_type: case i % 2
                  when 1
                    "non_recurring"
                  when 0
                    "recurring"
                  end,
        branch: branch,
        organization: branch.organization,
        last_activity_at: i.days.ago
      )
    end

    Job.import(jobs)
  end
end
