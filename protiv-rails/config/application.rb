# frozen_string_literal: true

require_relative "boot"

require "rails/all"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module Protiv
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.2

    # Please, add to the `ignore` list any other `lib` subdirectories that do
    # not contain `.rb` files, or that should not be reloaded or eager loaded.
    # Common ones are `templates`, `generators`, or `middleware`, for example.
    config.autoload_lib(ignore: %w[assets tasks])

    Rails.autoloaders.main.ignore(Rails.root.join("lib/generators/swagger_client/swagger_client_generator.rb"))

    # config.autoload_paths << "#{root}/app/services"

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
    config.active_record.schema_format = :sql

    config.active_job.queue_adapter = :sidekiq

    # FIXME: this isn't used
    config.x.aspire.base_url = "https://cloud-api.youraspire.com"
    config.x.aspire.async = true

    # Open emails with letter_opener
    config.action_mailer.delivery_method = :letter_opener
    config.active_job.queue_adapter = :sidekiq

    config.debug_exception_response_format = :api

    config.rails_host = ENV.fetch("PROTIV_RAILS_HOST", nil)
    config.api_host = ENV.fetch("PROTIV_API_HOST", nil)

    config.active_record.encryption.primary_key = ENV["ACTIVE_RECORD_ENCRYPTION_PRIMARY_KEY"]
    config.active_record.encryption.deterministic_key = ENV["ACTIVE_RECORD_ENCRYPTION_DETERMINISTIC_KEY"]
    config.active_record.encryption.key_derivation_salt = ENV["ACTIVE_RECORD_ENCRYPTION_KEY_DERIVATION_SALT"]

    config.skylight.environments << :development

    def ember_app
      config.ember_app
    end

    def enable_dev_portal?
      # TODO: disable this in production based on an ENV variable
      true
    end

    def redis_url
      ENV.fetch(ENV.fetch("REDIS_PROVIDER", nil) || "REDIS_URL", nil)
    end

    def redis
      @redis ||= ConnectionPool::Wrapper.new do
        Redis.new(url: redis_url)
      end
    end

    def schedule_cron?
      !ENV["DISABLE_SIDEKIQ_CRON"] && Rails.env.production?
    end

    def schedule_debounce_cron?
      !ENV["DISABLE_SIDEKIQ_CRON"] && !Rails.env.test?
    end

    unless config.eager_load
      config.to_prepare do
        sync_dir = Rails.root.join("app/services/sync")
        Rails.autoloaders.main.eager_load_dir(sync_dir)
      end
    end
  end
end
