# frozen_string_literal: true

source "https://rubygems.org"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 7.2.2"
# NOTE: https://github.com/rails/rails/issues/53606
# gem "rails", "~>8.0.0"

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem "sprockets-rails"
# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails"
# Use Tailwind CSS [https://github.com/rails/tailwindcss-rails]
gem "tailwindcss-rails"
# Use Redis adapter to run Action Cable in production
# gem "redis", ">= 4.0.1"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ windows jruby ]

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

gem "noticed"
gem "sidekiq"
gem "skylight"
gem "authentication-zero"
gem "faraday"
gem "jwt"
gem "letter_opener"
gem "graphiti", "~> 1.7.6"
gem "graphiti-rails"
gem "ostruct"
gem "pundit"
gem "rack-cors"
gem "kaminari"
gem "responders"
gem "zxcvbn-ruby", require: "zxcvbn" # https://github.com/envato/zxcvbn-ruby
gem "scenic"
gem "seedbank"
gem "activerecord-import"
gem "money-rails"
gem "aasm"
gem "redis", "~> 5"
gem "connection_pool"
gem "bugsnag"
gem "sidekiq-cron"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri windows ], require: "debug/prelude"

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false
  gem "bundler-audit", require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false

  gem "rspec-rails"
  gem "pry-byebug"
  gem "factory_bot_rails"
  gem "dotenv-rails"
  gem "graphiti_spec_helpers", github: "zvkemp/graphiti_spec_helpers", ref: "rspec-context"
  gem "foreman"
  gem "bullet"
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"
  gem "vandal_ui", github: "zvkemp/vandal_ui", branch: "no-install"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem "capybara"
  gem "selenium-webdriver"
  gem "rspec"
  gem "shoulda-matchers"
  gem "parallel_tests"
  gem "mock_redis"
  gem "vcr"
  gem "webmock"
  gem "simplecov", require: false
  gem "rails-controller-testing"
  gem "mock_redis"
end
