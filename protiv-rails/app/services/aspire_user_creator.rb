# frozen_string_literal: true

class AspireUserCreator
  def initialize(organization:)
    @organization = organization
    @created_users = []
    @errors = []
  end

  attr_reader :organization, :created_users, :errors

  # Create users for selected identities that don't have users yet
  # @param identity_ids [Array<Integer>] IDs of identities to create users for
  # @param identity_data [Hash] Additional data for each identity (keyed by identity_id)
  # @return [Hash] Result with created users and errors
  def create_users_for_identities(identity_ids, identity_data = {})
    return { created_users: [], user_records: [], errors: ["No identity IDs provided"] } if identity_ids.blank?

    # Find all identities that belong to the organization and don't have users yet
    identities = Identity.where(
      id: identity_ids,
      organization_id: organization.id,
      user_id: nil
    ).includes(:integration_records)

    Rails.logger.info("Found #{identities.size} identities without users")

    user_records = []

    identities.each do |identity|
      user = create_user_for_identity(identity, identity_data[identity.id.to_s])
      user_records << user if user.is_a?(User) && user.persisted?
    end

    { user_records: user_records, errors: @errors }
  end

  private

  def create_user_for_identity(identity, data = {})
    begin
      # Use provided data or fallback to identity data
      email = data&.dig(:email).presence || identity.email
      name = data&.dig(:name).presence || identity.name

      # Skip if we don't have an email
      if email.blank?
        @errors << { identity_id: identity.id, error: "Email is required to create a user" }
        return nil
      end

      # Create the user
      user = User.new(
        email: email,
        name: name,
        password: SecureRandom.hex(8) # Random password since they'll use SSO or reset password
      )

      # Save the user
      if user.save
        # Link the identity to the user
        identity.update(user_id: user.id)

        user
      else
        @errors << {
          identity_id: identity.id,
          error: "Failed to create user: #{user.errors.full_messages.join(', ')}"
        }
        nil
      end
    rescue => e
      Rails.logger.error("Error creating user for identity #{identity.id}: #{e.message}")
      Rails.logger.error(e.backtrace.join("\n"))
      @errors << { identity_id: identity.id, error: "Exception: #{e.message}" }
      nil
    end
  end
end
