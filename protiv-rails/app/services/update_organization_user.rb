# frozen_string_literal: true

# This service handles updating both role and user attributes for an organization user.
# It extracts attributes from the params and updates the role and user records.
class UpdateOrganizationUser < ApplicationService
  def initialize(role:, attributes: {})
    @role = role
    @attributes = attributes.symbolize_keys
  end

  def execute
    result = Result.new
    result.ok!

    begin
      ApplicationRecord.transaction do
        update_user_attributes
        update_role_attributes

        result.state = @role
      end
    rescue => e
      result.error!(e)
    end

    result
  end

  private

  def update_role_attributes
    role_attrs = extract_role_attributes
    @role.update!(role_attrs) if role_attrs.present?
  end

  def extract_role_attributes
    role_attrs = {}

    role_attrs[:active] = @attributes[:active] if @attributes.key?(:active)
    role_attrs[:role_type] = @attributes[:role_type] if @attributes.key?(:role_type)
    role_attrs[:status] = @attributes[:status] if @attributes.key?(:status)

    role_attrs[:wage] = @attributes[:wage] if @attributes.key?(:wage)

    role_attrs
  end

  def update_user_attributes
    user_attrs = extract_user_attributes
    @role.user.update!(user_attrs) if user_attrs.present?
  end

  def extract_user_attributes
    user_attrs = {}

    user_attrs[:name] = @attributes[:user_name] if @attributes[:user_name].present?
    user_attrs[:email] = @attributes[:user_email] if @attributes[:user_email].present?

    extract_phone_attributes(user_attrs)

    user_attrs
  end

  def extract_phone_attributes(user_attrs)
    if @attributes[:user_phone_country_code].present?
      user_attrs[:phone_country_code] = @attributes[:user_phone_country_code]
    end

    if @attributes[:user_phone].present?
      user_attrs[:phone] = @attributes[:user_phone]
    end

    user_attrs
  end
end
