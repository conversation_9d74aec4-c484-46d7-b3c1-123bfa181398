# frozen_string_literal: true

class ToggleOrganizationUserStatus < ApplicationService
  def initialize(role:, status:)
    @role = role
    @status = status
  end

  def execute
    result = Result.new
    result.ok!

    begin
      ApplicationRecord.transaction do
        case @status
        when "active"
          @role.make_active!
        when "inactive"
          @role.make_inactive!
        when "invited"
          @role.make_invited!
        when "uninvited"
          @role.make_uninvited!
        else
          raise ArgumentError, "Invalid status: #{@status}"
        end

        result.state = @role
      end
    rescue => e
      result.error!(e)
    end

    result
  end
end
