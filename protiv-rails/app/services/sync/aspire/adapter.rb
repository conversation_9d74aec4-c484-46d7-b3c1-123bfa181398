# frozen_string_literal: true

class Sync::Aspire::Adapter < Sync::Abstract::Adapter
  include Skylight::Helpers

  module ModelPatches
    module PropertyPatches
      def primary_contact_id
        property_contacts.find(&:primary_contact)&.contact_id
      end
    end

    AspireClient::Models::AspireCloudDomainTenantPropertiesODataModelProperty.prepend(PropertyPatches)
  end
  # NOTE: aspire refresh tokens expire 1 week after the access token

  # How far back from the latest record should we sync. This should not be needed, but we can use this to test that
  # hypothesis. It's conceivable that a Sync source could allow older records to come in with earlier dates after
  # we synced last. Only used for resources the we can detect the last modified date on Aspire
  SYNC_OVERLAP_SAFETY = 5.minutes # 1.day or 0

  def source
    Sync::Aspire.source
  end

  def remote_helper_models
    Sync::Aspire::REMOTE_HELPER_MODELS
  end

  def remote_index
    # map of aspire ids to protiv (activerecord primary keys)
    @remote_index ||= {}
  end

  def refresh_credential
    response = retrieve_credential
    expires_at = token_expiry(response.token)

    # save the new credentials and set the local variable
    @credential = (credential || integration.build_credential).update(
      bearer_token: response.token,
      refresh_token: response.refresh_token,
      expires_at:
    )

    # clear the existing client, if any
    @aspire_client = nil
  end

  def retrieve_credential
    auth = new_aspire_client(token: nil).authorization

    if credential&.refresh_token
      begin
        return auth.refresh_token(refresh_token: credential.refresh_token)
      rescue => e
        refresh_error = e
      end
    end

    auth.post(client_id:, secret:)
  rescue
    if refresh_error
      raise Sync::Errors::CredentialError, refresh_error
    else
      raise Sync::Errors::CredentialError
    end
  end

  def aspire_users
    @aspire_users ||= retrieve_users.map(&:first).index_by(&:user_id)
  end

  def _retrieve(**options)
    Retrieve.new(self, **options).execute
  end

  def retrieve_identities(**options)
    _retrieve(
      remote_resource: :contact,
      filters: ["ContactTypeName eq 'Sub' or ContactTypeName eq 'Employee'"],
      updated_records_filter: ->(date) { "(CreatedDateTime ge #{date} or ModifiedDate ge #{date})" },
      order_by: nil, # FIXME: is this needed? "ModifiedDate",
      **options
    )
  end

  def retrieve_services(**options)
    _retrieve(
      remote_resource: :service,
      filters: ["Active eq true"],
      updated_records_filter: nil, # TODO: Aspire doesn't expose timestamps here
      **options
    )
  end

  def retrieve_item_allocations(**options)
    _retrieve(
      remote_resource: :item_allocation,
      updated_records_filter: ->(date) {
        "(LastModifiedDateTime ge #{date} or CreatedDateTime ge #{date})"
      },
      embedded_dependencies: {
        catalog_item: [
          :catalog_item_id,
          ->(item_allocation_record) do
            map_embedded_catalog_item_from_item_allocation(item_allocation_record)
          end
        ]
      },
      **options
    )
  end

  def retrieve_milestone_items(**options)
    _retrieve(
      remote_resource: :work_ticket_item,
      updated_records_filter: ->(date) {
        # NOTE: they don't have LastModifiedDateTime on there
        "CreatedDateTime ge #{date}"
      },
      resolve_dependencies: {
        work_ticket: :work_ticket_id
      },
      embedded_dependencies: {
        catalog_item: [
          :catalog_item_id,
          # We don't want/need to fetch catalog items directly, but the relevant metadata is already here.
          ->(work_ticket_record) do
            map_embedded_catalog_item_from_work_ticket(work_ticket_record)
          end
        ]
      },
      **options
    )
  end

  def retrieve_catalog_items(**options)
    # NOTE: for aspire, catalog items are fetched as embedded dependencies from
    # milestone item or item allocation;
    # this method is here to ensure the syncer interface is implemented.
    []
  end

  instrument_method
  def retrieve_users(**options)
    _retrieve(remote_resource: :user, **options)
  end

  # NOTE: this alias is necessary to resolve dependencies,
  # since jobs<->opportunities isn't a first-class mapping anymore
  def retrieve_opportunities(**options)
    retrieve_jobs(**options)
  end

  instrument_method
  def retrieve_jobs(**options)
    _retrieve(
      remote_resource: :opportunity,
      initial_sync_filters: [
        "(OpportunityStatus eq 'Won')",
        "(JobStatusName ne 'Complete')",
        "(JobStatusName ne 'Canceled')"
      ],
      updated_records_filter: ->(date) {
        "(CreatedDateTime ge #{date} or ModifiedDate ge #{date})"
      },
      order_by: "ModifiedDate",
      resolve_dependencies: {
        contact: :operations_manager_contact_id,
        branch: :branch_id,
        property: :property_id
      },
      reverse_dependencies: {
        numbered_opportunity: :opportunity_number
      },
      segmented_initial_sync: true,
      **options
    )
  end

  instrument_method
  def retrieve_branches(**options)
    _retrieve(
      remote_resource: :branch,
      updated_records_filter: nil, # NOTE: aspire api has no timestamps here
      **options
    )
  end

  instrument_method
  def retrieve_properties(**options)
    _retrieve(
      remote_resource: :property,
      updated_records_filter: ->(date) { "(CreatedDate ge #{date} or ModifiedDate ge #{date})" },
      resolve_dependencies: {
        contact: :primary_contact_id
      },
      **options
    )
  end

  instrument_method
  def retrieve_milestones(**options)
    _retrieve(
      remote_resource: :work_ticket,
      updated_records_filter: ->(date) { "(CreatedDateTime ge #{date} or LastModifiedDateTime ge #{date})" },
      order_by: "LastModifiedDateTime",
      resolve_dependencies: {
        opportunity: :opportunity_id,
        opportunity_service: :opportunity_service_id
      },
      # FIXME: this doesn't really work, as we don't have the primary keys
      # reverse_dependencies: {
      #   item_allocation: [:work_ticket_id]
      # },
      **options
    )
  end

  instrument_method
  def retrieve_milestone_times(**options)
    _retrieve(
      remote_resource: :work_ticket_time,
      updated_records_filter: ->(date) { "(CreatedDateTime ge #{date} or LastModifiedDateTime ge #{date})" },
      order_by: "LastModifiedDateTime",
      resolve_dependencies: {
        work_ticket: :work_ticket_id,
        contact: :contact_id,
        route: :route_id
      },
      **options
    )
  end

  instrument_method
  def retrieve_routes(**options)
    _retrieve(
      remote_resource: :route,
      updated_records_filter: nil, # TODO: routes doesn't have timestamps in the Aspire API; is there another way to optimize updates?
      order_by: nil,
      resolve_dependencies: {
        contact: :crew_leader_contact_id,
        branch: :branch_id
      },
      **options
    )
  end

  instrument_method
  def retrieve_clock_times(sync_status:, **options)
    filters = []

    last_synced_at = sync_status&.last_synced_at

    if last_synced_at
      last_synced_at -= SYNC_OVERLAP_SAFETY
    end

    initial_sync = last_synced_at.blank?
    date_range = sync_status.date_range if sync_status.respond_to?(:date_range)

    if date_range
      range0 = date_range.first.utc.iso8601(6)
      range1 = date_range.last.utc.iso8601(6)

      filters << "ClockStart ge #{range0} and ClockStart le #{range1}"
      filters << "AcceptedDateTime eq null" unless initial_sync
    elsif last_synced_at
      filters << "AcceptedDateTime ge #{last_synced_at.iso8601(6)}"
    end

    _retrieve(
      remote_resource: :clock_time,
      filters:,
      resolve_dependencies: { contact: :contact_id },
      **options
    )
  end

  def initial_sync_done?
    integration.initial_sync_done?
  end

  def sync_job_initial(job)
    sync_job_milestones(job)
  end

  def remote_resource_for(local_resource)
    Sync::Aspire::RESOURCE_MAP.invert.fetch(local_resource.to_sym)
  end

  def local_resource_for(remote_resource)
    Sync::Aspire::RESOURCE_MAP[remote_resource.to_sym]
  end

  def has_initial_sync_stage?(local_resource)
    return false unless local_resource

    case local_resource.to_sym
    when :milestones, :milestone_times, :clock_times, :item_allocations, :milestone_items
      true
    else
      false
    end
  end

  def ranged_clock_times?
    true
  end

  # NOTE: this is essentially unconfigured; only use for remote types that have no local type
  def retrieve_remote(remote_resource:, **options)
    retrieve_method = "retrieve_#{remote_resource.to_s.pluralize}"

    if respond_to?(retrieve_method)
      send(retrieve_method, **options)
    else
      # FIXME: maybe eagerly request to local type if it exists?
      _retrieve(
        remote_resource:,
        **options
      )
    end
  end

  def retrieve_opportunity_services(**options)
    _retrieve(
      remote_resource: :opportunity_service,
      updated_records_filter: ->(date) { "LastModifiedDateTime ge #{date}" },
      resolve_dependencies: {
        service: :service_id
      },
      **options
    )
  end

  def retrieve_numbered_opportunities(ids:, **options)
    # NOTE: this does not correspond to an Aspire resource.
    # It is a phantom marker that represents a single aspire Job,
    # keyed on Opportunity#OpportunityNumber.

    ids.map do |id|
      Sync::Aspire::NumberedOpportunity.new(id)
    end
  end

  def create_clock_time(attendance)
    if attendance.milestone
      sync_attendance_as_milestone_time(attendance)
    else
      sync_attendance_as_clock_time(attendance)
    end
  end

  alias_method :create_milestone_time, :create_clock_time

  private

  def map_embedded_catalog_item_from_work_ticket(work_ticket)
    catalog_item = AspireClient::Models::AspireCloudDomainTenantCatalogItemsODataModelCatalogItem.new
    catalog_item.catalog_item_id = work_ticket.catalog_item_id.presence or return nil
    catalog_item.item_type = work_ticket.item_type
    catalog_item.item_name = work_ticket.item_name
    catalog_item.allocation_unit_type_name = work_ticket.allocation_unit_type_name
    catalog_item
  end

  def map_embedded_catalog_item_from_item_allocation(item_allocation)
    catalog_item = AspireClient::Models::AspireCloudDomainTenantCatalogItemsODataModelCatalogItem.new
    catalog_item.catalog_item_id = item_allocation.catalog_item_id.presence or return nil
    catalog_item.item_type = item_allocation.item_type
    catalog_item.item_name = item_allocation.item_name
    # NOTE: ItemAllocation doesn't have unit type
    # catalog_item.allocation_unit_type_name = item_allocation.allocation_unit_type_name
    catalog_item
  end

  def aspire_client
    return @aspire_client if @aspire_client.present?

    if credential.blank? || credential.expires_at&.<(2.hours.from_now)
      refresh_credential
    end

    @aspire_client = new_aspire_client(token: credential.bearer_token)
  end

  def new_aspire_client(token: nil)
    AspireClient::Client.new(token:, client_id: integration.client_id)
  end

  def decode_token(jwt)
    JWT.decode(jwt, nil, false)[0]
  end

  def token_expiry(jwt)
    Time.zone.at(decode_token(jwt).fetch("exp"))
  end

  def track_error(data = {}, &block)
    Sync::ErrorTracking.track(data, &block)
  end

  def sync_attendance_as_milestone_time(attendance)
    work_ticket_time = AspireClient::Models::AspireCloudExternalApiWorkTicketTimesModelWorkTicketTimeInsertRequest.new({})

    work_ticket_time.start_time = attendance.started_at.iso8601(6)
    work_ticket_time.end_time = attendance.ended_at.iso8601(6)

    started_geolocation = attendance.started_geolocation
    work_ticket_time.start_latitude = started_geolocation[:latitude]
    work_ticket_time.start_longitude = started_geolocation[:longitude]

    ended_geolocation = attendance.started_geolocation
    work_ticket_time.end_latitude = ended_geolocation[:latitude]
    work_ticket_time.end_longitude = ended_geolocation[:longitude]

    work_ticket_time.work_ticket_id = remote_id!(attendance.milestone)
    work_ticket_time.contact_id = remote_id!(attendance.identity)
    work_ticket_time.route_id = remote_id!(attendance.route)
    work_ticket_time.crew_leader_contact_id = remote_id!(attendance.manager)

    remote_id = aspire_client.work_ticket_times.post(**work_ticket_time.as_json)

    { source:, remote_resource: "work_ticket_time", remote_id: }
  end

  def sync_attendance_as_clock_time(attendance)
    clock_time = AspireClient::Models::AspireCloudExternalApiClockTimesModelClockTimeInsertRequest.new({})
    clock_time.contact_id = remote_id!(attendance.identity)
    clock_time.clock_start_date_time = attendance.started_at.iso8601(6)
    clock_time.clock_end_date_time = attendance.ended_at.iso8601(6)
    clock_time.break_time = attendance.break_time_hours

    started_geolocation = attendance.started_geolocation
    clock_time.clock_start_lat = started_geolocation[:latitude]
    clock_time.clock_start_long = started_geolocation[:longitude]

    ended_geolocation = attendance.ended_geolocation
    clock_time.clock_end_lat = ended_geolocation[:latitude]
    clock_time.clock_end_long = ended_geolocation[:longitude]

    clock_time.route_id = remote_id!(attendance.route)
    clock_time.crew_leader_contact_id = remote_id!(attendance.manager)

    remote_id = aspire_client.clock_times.post(**clock_time.as_json)

    { source:, remote_resource: "clock_time", remote_id: }
  end

  def remote_id!(record)
    return unless record

    integration_record = record.integration_records.merge(integration_records).first

    raise "missing integration record for integration=#{integration.id}; #{record.class} #{record.id}" unless integration_record
    integration_record.remote_id
  end

  class Retrieve
    def initialize(
      adapter,
      remote_resource:,
      resolve_dependencies: [],
      reverse_dependencies: [],
      embedded_dependencies: [],
      updated_records_filter: nil,
      params: {},
      filters: [],
      segmented_initial_sync: false,
      sync_status: nil,
      ids: nil,
      **options
    )
      @adapter = adapter
      @sync_status = sync_status
      @params = params
      @filters = filters
      @remote_resource = remote_resource.to_s
      @initial_sync_filters = options[:initial_sync_filters] || []
      @resolve_dependencies = resolve_dependencies
      @reverse_dependencies = reverse_dependencies
      @embedded_dependencies = embedded_dependencies
      @updated_records_filter = updated_records_filter
      @segmented_initial_sync = segmented_initial_sync
      @order_by = options[:order_by]
      @ids = ids
      @options = options
    end

    attr_reader \
      :sync_status,
      :options,
      :params,
      :initial_sync_filters,
      :resolve_dependencies,
      :reverse_dependencies,
      :embedded_dependencies,
      :adapter,
      :remote_resource,
      :order_by,
      :updated_records_filter,
      :ids

    def last_synced_at
      sync_status&.last_synced_at
    end

    def execute
      if !ids && initial_sync? && @segmented_initial_sync
        # FIXME: segmented_paginate is too different from paginate
        segmented_paginate(
          filters: computed_filters,
          order_by:,
        )
      else
        paginate(
          computed_params,
          segments: ids,
          segment_key: options[:segment_key] || default_segment_key
        )
      end
    end

    private

    def integration_records
      adapter.integration_records
    end

    def sync_caches
      adapter.integration.sync_caches
    end

    def on_page(context)
    end

    def retrieve(paginated_params)
      adapter.send(:aspire_client).public_send(remote_resource.pluralize).get(query: paginated_params)
    end

    def computed_params
      @computed_params ||= begin
        p = @params.dup

        if (filters = computed_filters&.join(" and ").presence)
          p["$filter"] = filters
        end

        p["$orderby"] = @order_by if @order_by
        p
      end
    end

    def computed_filters
      return nil if ids

      @computed_filters ||= begin
        f = @filters.dup

        if initial_sync?
          f |= initial_sync_filters
        elsif updated_records_filter && last_synced_at
          date = (last_synced_at - SYNC_OVERLAP_SAFETY).utc.iso8601(6)
          f << updated_records_filter.call(date)
        end

        f
      end
    end

    def initial_sync?
      last_synced_at.blank?
    end

    def resource_id_key
      @resource_id_key ||= "#{remote_resource}_id"
    end

    def remote_resource_id_key(name = remote_resource)
      "#{name.to_s.singularize.camelize}ID"
    end

    def remote_resource_id_method(name = remote_resource)
      "#{name}_id"
    end

    def default_segment_key
      remote_resource_id_key
    end

    DEFAULT_PAGE_LIMIT = 1000
    DEFAULT_SEGMENT_SIZE = 50

    # NOTE: the default page size for Aspire is quite large;
    # if this ends up taking too much memory it can be reduced here.
    # The enumerator should ensure we aren't loading more than the limit
    # of records at any one time (per thread)
    #
    # Segmentable lists should be given as:
    # { "ContactID" => [...] }
    # Note that multiple segmented keys are not supported.
    def paginate(params, context: {}, segments: nil, segment_key: nil)
      params["$limit"] ||= DEFAULT_PAGE_LIMIT
      params["$pageNumber"] = 0

      # NOTE: context is generally scoped to a pagination call; it's useful for
      # e.g. temporary indexes or other values that shouldn't outlive the syncing of one resource.
      # The current page is attached to it, and it may be optionally supplied by the caller.
      if segments.nil?
        _page_enum(params, context)
      else
        return [] if segments.empty?
        raise ArgumentError, "segment key is required" if segment_key.blank?
        segments.each_slice(DEFAULT_SEGMENT_SIZE).map do |segment|
          segment_filter =
            if segment.one?
              "#{segment_key} eq #{segment.first}"
            else
              "#{segment_key} in (#{segment.join(',')})"
            end

          if (filter = params["$filter"].presence)
            segment_filter = [filter, segment_filter].compact.join(" and ")
          end

          _page_enum(params.merge("$filter" => segment_filter), context)
          # NOTE: this to_enum is necessary to support some enumerable methods that
          # don't appear on Enumerator::Chain
        end.inject(:chain).to_enum
      end
    end

    def _page_enum(params, context)
      Enumerator.new do |yielder|
        loop do
          params["$pageNumber"] += 1
          result = retrieve(params)

          context[:page] = result
          on_page(context)

          result.each do |record|
            wrapper = Sync::Aspire::Record.new(record, remote_resource:)

            resolve_dependencies.each do |remote_resource, key|
              if (value = record.public_send(key))
                wrapper.add_dependency(remote_resource, value)
              end
            end

            reverse_dependencies.each do |remote_resource, key|
              if (value = record.public_send(key))
                wrapper.add_reverse_dependency(remote_resource, value)
              end
            end

            embedded_dependencies.each do |remote_resource, (key, mapper)|
              if (primary_key = record.public_send(key)) && mapper.respond_to?(:call)
                if (embedded_record = mapper.call(record))
                  wrapper.add_embedded_dependency(remote_resource, primary_key, mapper.call(record))
                end
              end
            end

            yielder << [wrapper, context]
          end

          break if result.length < params["$limit"]
        end
      end
    end

    # FIXME: use another abstract pagination method?
    def segmented_paginate(filters: [], order_by: nil, comparison_field: "ModifiedDate")
      params = {}
      params["$orderby"] = order_by if order_by

      t = Time.now.utc
      now = t

      segments = []

      # - each of the last 7 days
      segments << { min: now -= 24.hours }
      6.times { segments << { max: now, min: now -= 24.hours } }

      # - up to 3 months preceding today, by week
      11.times do
        segments << { max: now, min: now -= 1.week }
      end

      # anything older
      segments << { max: now }

      context = {}

      segments.map do |segment|
        date_filters = []

        if (max = segment[:max])
          max = (max + SYNC_OVERLAP_SAFETY).utc.iso8601(6)
          date_filters << "(#{comparison_field} le #{max})"
        end

        if (min = segment[:min])
          min = (min - SYNC_OVERLAP_SAFETY).utc.iso8601(6)
          date_filters << "(#{comparison_field} ge #{min})"
        end

        filters ||= []
        filter = (filters | date_filters).join(" and ")

        Rails.logger.info("segment: #{filter}")
        context[:segment] = segment

        paginate(
          params.merge("$filter" => filter),
          context:,
        )
        # NOTE: this to_enum is necessary to support some enumerable methods that
        # don't appear on Enumerator::Chain
      end.inject(:chain).to_enum
    end
  end
end
