# frozen_string_literal: true

module Sync::Aspire
  extend Sync::CommonClassMethods

  SOURCE = "aspire"

  ALLOW_MATERIALIZED_PLACEHOLDERS = [:contact]

  # NOTE: do not add phantom resources to this map
  # (e.g., NumberedOpportunity)
  RESOURCE_MAP = {
    contact: :identity,
    branch: :branch,
    property: :property,
    work_ticket: :milestone,
    work_ticket_time: :milestone_time,
    work_ticket_item: :milestone_item,
    item_allocation: :item_allocation,
    route: :route,
    clock_time: :clock_time,
    service: :service,
    catalog_item: :catalog_item
  }.freeze

  SLUGGABLE_RESOURCES = [:numbered_opportunity, *RESOURCE_MAP.keys]

  # Things that aren't mapped to a core resource, but should
  # be monitored for updates.
  REMOTE_HELPER_MODELS = [
    :opportunity_service
  ]

  CAPABILITIES = {
    jobs: [:pull].freeze,
    milestones: [:pull].freeze,
    milestone_times: [:pull, :create].freeze,
    identities: [:pull].freeze,
    branches: [:pull].freeze,
    routes: [:pull].freeze,
    services: [:pull].freeze,
    catalog_items: [:pull].freeze,
    milestone_items: [:pull].freeze,
    item_allocations: [:pull].freeze,
    clock_times: [
      :pull,
      :create
    ].freeze
  }.freeze

  def self.capabilities
    CAPABILITIES
  end

  def self.materializer
    Materializer
  end

  def self.adapter
    Adapter
  end

  def self.source
    SOURCE
  end

  def self.remote_helper_models
    REMOTE_HELPER_MODELS
  end

  def self.cache_to_resource(cache)
    resource = cache.remote_resource.to_s.pluralize

    if resource == "numbered_opportunities"
      Sync::Aspire::NumberedOpportunity.new(cache.raw_data.fetch("OpportunityNumber"))
    else
      AspireClient::Resources.response_type(cache.remote_resource.pluralize).new(cache.raw_data)
    end
  end

  ::Sync.register_module(self)
end
