# frozen_string_literal: true

class Sync::Abstract::Adapter
  # Terms:
  # remote_slug: string, format "source:resource:id", e.g. "aspire:contact:123"
  # remote_id: a remote primary key id or uuid
  # local_id: a local primary key id
  #
  DEFAULT_REFRESH_THRESHOLD = 1.hour

  def self.refresh_threshold
    DEFAULT_REFRESH_THRESHOLD
  end

  attr_reader :integration

  def initialize(integration)
    @integration = integration
  end

  delegate \
    :credential,
    :changesets,
    :integration_records,
    :organization,
    :client_id,
    :secret,
    to: :integration

  # Array of class names (singular) to create stub functions for
  SYNC_CLASSES = %w[
    identity
    job
    branch
    property
    item_allocation
    milestone
    milestone_item
    milestone_time
    route
    clock_time
    catalog_item
  ]

  def credential
    integration.credential
  end

  def maybe_refresh_credential
    if credential.expires_at&.<(refresh_threshold.from_now)
      refresh_credential
    end
  end

  def refresh_threshold
    self.class.refresh_threshold
  end

  def retrieve_branches(selection = {})
    not_implemented!
  end

  def remote_resource_for(local_resource)
    not_implemented!
  end

  def has_initial_sync_stage?(_local_resource)
    false
  end

  # Whether or not this integration tracks clock times by SyncStatusRanges
  def ranged_clock_times?
    false
  end

  private

  # Create sync class private functions. For example:
  #   def sync_location(location)
  #     not_implemented!
  #   end
  #
  #   def sync_identity(identity)
  #     not_implemented!
  #   end
  #
  SYNC_CLASSES.each do |class_name|
    define_method "sync_#{class_name}" do |*|
      not_implemented!
    end

    define_method "#{class_name}_records" do
      integration_records.where(record_type: class_name.camelize)
    end
  end

  def not_implemented!(method_name = nil)
    method_name ||= caller_locations.first.label
    raise "`##{method_name}' not implemented for #{self.class}"
  end
end
