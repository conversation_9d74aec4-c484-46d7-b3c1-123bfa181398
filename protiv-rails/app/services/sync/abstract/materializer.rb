# frozen_string_literal: true

class Sync::Abstract::Materializer
  def initialize(integration)
    @integration = integration
  end

  attr_reader :integration

  def self.parent_module
    module_parents.first
  end

  include ActiveSupport::Callbacks

  delegate \
    :integration_records,
    :organization,
    :organization_id,
    :adapter,
    :cache_adapter,
    :tracking_sync,
    :sync_caches,
    to: :integration

  def materialize_caches(caches)
    caches.group_by(&:remote_resource).each do |remote_resource, resource_caches|
      local_resource = adapter.local_resource_for(remote_resource)

      send("materialize_#{local_resource.to_s.pluralize}", caches: resource_caches)
    end
  end

  %w[
    identities
    branches
    properties
    routes
    jobs
    milestones
    milestone_times
    services
    catalog_items
    milestone_items
    item_allocations
  ].each do |resource|
    define_callbacks :"materialize_#{resource}"

    define_method("materialize_#{resource}") do |caches: nil, **options|
      # Caches may be supplied if it is necessary to materialize
      # some things we already have in memory. Caches may also include caches to
      # dependencies of other types, which are passed to the materialize_* methods.
      if caches
        remote_resource = remote_resource(resource).to_s

        # Since we're not doing a full sync, don't set the current time on the sync status.
        tracking_sync(:"materialize_#{resource}", sync_status: false) do
          run_callbacks("materialize_#{resource}") do
            caches.select { |x| x.remote_resource.to_s == remote_resource }.each do |cache|
              raise "not in syncing context" if Sync::Current.integration_id.nil?
              send("materialize_#{resource.singularize}", cache.to_resource, caches:, **options)
            end
          end
        end
      else
        if adapter.has_initial_sync_stage?(resource) && !integration.initial_resource_sync_done?(resource) && !options[:initial_sync]
          return
        end

        # Otherwise, load caches from the database
        tracking_sync(:"materialize_#{resource}") do |sync_status|
          run_callbacks("materialize_#{resource}") do
            send("unmaterialized_#{resource}", sync_status.last_synced_at).find_in_batches do |batch|
              load_page_into_remote_index(batch)

              materialize_batch(batch) do |cache|
                raise "not in syncing context" if Sync::Current.integration_id.nil?
                send("materialize_#{resource.singularize}", cache.to_resource, **options)
              end
            end
          end
        end
      end
    end

    define_method("unmaterialized_#{resource}") do |last_synced_at|
      _unmaterialized_resource(:"#{resource.singularize}", last_synced_at)
    end
  end

  def remote_slug(remote_id, resource_type)
    raise ArgumentError, "remote_id is nil" if remote_id.nil?
    raise ArgumentError, "resource_type is nil" if resource_type.nil?

    "#{source}:#{resource_type}:#{remote_id}"
  end

  private

  # We used a pretty wide definition of 'last_synced_at', so espeically on
  # the first sync after the initial sync, this step can take an awfully long time
  # (it's essentially re-verifying the already-synced data).
  #
  # We can more accurately pinpoint unsynced milestones in this batching
  # stage, and skip verifying things we know are already synced with the cache.
  def materialize_batch(batch)
    slugs = batch.map do |cache|
      [cache.remote_slug, cache.updated_at]
    end.to_h

    records = integration.integration_records.where(remote_slug: slugs.keys).pluck(:remote_slug, :updated_at).to_h

    records.each do |slug, updated_at|
      slugs.delete(slug) if slugs[slug] < updated_at
    end

    batch.each do |cache|
      next unless slugs.key?(cache.remote_slug)

      yield cache
    end
  end

  def track_error(data = {}, &block)
    Sync::ErrorTracking.track(data, &block)
  end

  # Map from remote_slug to local primary id
  def remote_index
    @remote_index ||= {}
  end

  def load_page_into_remote_index(batch)
    remote_slugs = batch.map(&:remote_slug) - remote_index.keys

    integration_records.where(remote_slug: remote_slugs).pluck(:remote_slug, :record_id).each do |remote_slug, record_id|
      remote_index[remote_slug] = record_id
    end
  end

  def _unmaterialized_resource(resource_name, last_synced_at)
    if (remote_resource = self.class.parent_module.const_get(:RESOURCE_MAP).invert[resource_name])
      sync_caches.cached.last_synced_after(last_synced_at).where(remote_resource:)
    else
      not_implemented!("unmaterialized resource getter for #{resource_name}")
    end
  end

  def not_implemented!(thing)
    raise "Expected implementation of #{thing}"
  end
end
