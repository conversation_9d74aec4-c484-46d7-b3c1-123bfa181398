# frozen_string_literal: true

class AspireRoleMappingPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      if user && user.admin_organizations.include?(organization)
        scope.where(organization: organization)
      else
        scope.none
      end
    end
  end

  def index?
    user.admin_organizations.include?(organization)
  end

  def show?
    user.admin_organizations.include?(organization)
  end

  def create?
    user.admin_organizations.include?(organization)
  end

  def update?
    user.admin_organizations.include?(organization)
  end

  def destroy?
    user.admin_organizations.include?(organization)
  end
end
