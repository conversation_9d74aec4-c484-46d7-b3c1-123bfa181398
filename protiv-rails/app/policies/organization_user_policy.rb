# frozen_string_literal: true

class OrganizationUserPolicy < ApplicationPolicy
  def index?
    user.organizations.include?(organization)
  end

  def show?
    user.organizations.include?(organization)
  end

  def create?
    user.admin_organizations.include?(organization) ||
      user.manager_organizations.include?(organization)
  end

  def update?
    user.admin_organizations.include?(organization) ||
      (user.manager_organizations.include?(organization) && record.role_type != "admin")
  end

  def activate?
    update?
  end

  def deactivate?
    update?
  end

  def invite?
    update?
  end

  def resend_invitation?
    update?
  end

  def uninvite?
    update?
  end

  def destroy?
    false # We don't allow deletion, only deactivation
  end

  class Scope < Scope
    def resolve
      if user && organization
        # Use unscoped to include both active and inactive roles
        scope.unscoped.where(organization: organization)
      else
        Role.none
      end
    end
  end
end
