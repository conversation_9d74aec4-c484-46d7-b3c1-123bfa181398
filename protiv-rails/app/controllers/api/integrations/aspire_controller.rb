# frozen_string_literal: true

module Api
  module Integrations
    class AspireController < Api::BaseController
      before_action :require_admin_role

      def create
        # Authorization is handled by the require_admin_role before_action

        integration = Integration.find_or_initialize_by(
          organization: current_organization,
          source: :aspire
        )

        integration.client_id = params[:client_id]
        integration.secret = params[:secret_key]
        integration.status = :pending if integration.respond_to?(:status=)

        if integration.save
          # Optionally trigger background sync
          integration.sync_all(async: true) if integration.respond_to?(:sync_all)

          render json: { message: "Aspire credentials saved." }, status: :ok
        else
          render json: { errors: integration.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def roles
        if @integration.nil?
          render json: { error: "Aspire integration not found" }, status: :not_found
          return
        end

        begin
          role_mapper = AspireRoleMapper.new(integration: @integration)
          # Use cached version to avoid API calls on every request
          unique_roles = role_mapper.cached_unique_role_names

          # Simple raw data response with just the unique roles
          render json: unique_roles, status: :ok
        rescue => e
          Rails.logger.error("Error retrieving Aspire roles: #{e.message}")
          render json: { error: "Error retrieving Aspire roles", details: e.message }, status: :internal_server_error
        end
      end

      # Retrieves user data from Aspire with their mapped roles
      def aspire_users_with_roles
        if @integration.nil?
          render json: { error: "Aspire integration not found" }, status: :not_found
          return
        end

        render jsonapi: AspireUserWithRolesResource.all(params)
      end

      # Create users for selected identities
      def create_users_for_identities
        begin
          deserialized = extract_identities_from_jsonapi

          if deserialized[:identity_ids].blank?
            render json: { error: "Missing identity IDs" }, status: :bad_request
            return
          end

          result = create_users(deserialized[:identity_ids], deserialized[:identity_data])
          if result[:user_records].present?
            render json: { message: "Users created", created_users: result[:user_records].size, errors: result[:errors] }, status: :ok
          else
            render json: { message: "No user created", errors: result[:errors] }, status: :unprocessable_entity
          end
        rescue => e
          handle_exception(e)
        end
      end

      private

      def extract_identities_from_jsonapi
        data = params[:data] || []

        identity_ids = data.map { |item| item[:id].to_s }
        identity_data = data.each_with_object({}) do |item, hash|
          hash[item[:id].to_s] = item[:attributes]&.slice(:email, :name)
        end

        { identity_ids: identity_ids, identity_data: identity_data }
      end

      def create_users(identity_ids, identity_data)
        AspireUserCreator.new(
          organization: current_organization,
        ).create_users_for_identities(identity_ids, identity_data)
      end

      def handle_exception(e)
        Rails.logger.error("Error creating users: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        render json: { error: "An error occurred: #{e.message}" }, status: :internal_server_error
      end

      def find_integration
        @integration = Integration.find_by(
          organization: current_organization,
          source: :aspire
        )
      end
    end
  end
end
