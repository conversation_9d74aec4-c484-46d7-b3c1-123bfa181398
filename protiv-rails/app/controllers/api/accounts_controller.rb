# frozen_string_literal: true

class Api::AccountsController < Api::BaseController
  skip_before_action :authenticate, only: %i[create]

  def show
    respond_with AccountResource.find(params.merge(include: ["user", "organizations"]))
  end

  def create
    account = AccountResource.build(params.merge(include: :user))

    if account.save
      render jsonapi: account, status: 201
    else
      render jsonapi_errors: account
    end
  end
end
