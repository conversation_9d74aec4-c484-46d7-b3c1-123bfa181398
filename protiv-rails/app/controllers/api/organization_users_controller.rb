# frozen_string_literal: true

class Api::OrganizationUsersController < Api::BaseController
  include Pundit::Authorization

  before_action :set_role, only: [:show, :update, :activate, :deactivate]
  before_action :authorize_action

  def index
    respond_with OrganizationUserResource.all(apply_organization_filter(params))
  end

  def show
    Rails.logger.info(current_user)
    respond_with OrganizationUserResource.find(apply_organization_filter(params))
  end

  def update
    attributes = params.dig(:data, :attributes)&.to_unsafe_h || {}
    result = UpdateOrganizationUser.new(role: @role, attributes: attributes).execute

    handle_result(result, "Organization user updated.")
  end

  def activate
    result = ToggleOrganizationUserStatus.new(role: @role, status: "active").execute
    handle_result(result, "User has been activated.")
  end

  def deactivate
    result = ToggleOrganizationUserStatus.new(role: @role, status: "inactive").execute
    handle_result(result, "User has been deactivated.")
  end

  private

  def set_role
    @role = Role.unscoped.find(params[:id])
  end

  def authorize_action
    authorize :organization_user
  end

  def handle_result(result, success_message)
    if result.ok?
      render jsonapi: { message: success_message }, status: :ok
    else
      render jsonapi_errors: { detail: result.error.message }, status: :unprocessable_entity
    end
  end

  def apply_organization_filter(params_hash)
    if params_hash[:organization_id].present?
      filter_params = params_hash.dup
      filter_params[:filter] ||= {}
      filter_params[:filter][:organization_id] = params_hash[:organization_id]
      filter_params
    else
      params_hash
    end
  end
end
