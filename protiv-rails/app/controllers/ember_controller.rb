# frozen_string_literal: true

##
# And essentially this controller exists to generate an index that HTML file that runs the frontend app.
# So a couple of important characteristics about it are that when you make an HTTP request like <PERSON>s can
# set a cookie so we can set persistent data in the session. And that's kind of what we use it for.
# This is kind of the bridge between the Rails session and the frontend API token base.
class EmberController < ApplicationController
  include PermittedRedirectHelper

  allow_sign_in_token_from_params!
  skip_before_action :authenticate, only: %i[marketing bootstrap sign_in sign_up sign_out password_reset]

  def marketing
    render_index
  end

  def onboarding
    render_index
  end

  def dashboard
    render_index
  end

  def password_reset
    render_index
  end

  if Rails.application.enable_dev_portal?
    def dev
      render_index
    end
  end

  def bootstrap
    render js: Rails.application.ember_app.bootstrap_js(current_user, current_organization)
  end

  def passwordless
    redirect_to redirect_location || dashboard_path
  end

  def sign_in
    if current_user
      redirect_to dashboard_path
    else
      render_index
    end
  end

  def sign_up
    sign_out_user
    render_index
  end

  def sign_out
    sign_out_user
    redirect_to redirect_location || root_path
  end

  private

  def render_index
    render body: Rails.application.ember_app.index_html(current_user, current_organization), content_type: "text/html"
  end

  BOOTSTRAP_ORIGINS = ["http://127.0.0.1:4200", "http://localhost:4200"].freeze

  def verify_same_origin_request
    # By default, Rails apply CSRF protections to non-XHR JavaScript requests.
    # This makes sense, since this could be a vector for leaking potentially
    # sensitive data to any sites, `<script src="https://example.com/some.js">`
    # is not protected by same-origin policy in the browser.
    #
    # Here we deliberately want to expose `/bootstrap.js` to the frontend dev
    # server on :4200, but Rails doesn't really give you any fine-grained
    # control over this logic – you only choice is to disable the protection
    # logic altogether.
    #
    # The frontend app makes this request with `crossorigin="use-credentials"`
    # so here we can check for that and reject other origins. This strategy
    # is used in development, so it's not super critical, but it's still good
    # to avoid arbitrary sites from being able to embed this as a `<script>`
    # tag and get access to our dev tokens, etc.
    if Rails.env.development? && params[:action] == "bootstrap" && BOOTSTRAP_ORIGINS.include?(request.origin)
      nil
    else
      super
    end
  end

  def handle_invalid_token_signature(error)
    session.clear
    redirect_to sign_in_path
  end

  def redirect_location
    permitted_redirect[:redirect]
  end
end
