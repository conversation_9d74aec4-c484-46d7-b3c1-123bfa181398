# frozen_string_literal: true

class ProPayResource < ApplicationResource
  has_many :sub_pro_pays

  filter :milestone_id, :integer, only: [:eq] do
    eq do |scope, value|
      scope.joins(sub_pro_pays: :sub_pro_pay_pro_payables).
        merge(SubProPayProPayable.where(pro_payable_id: value, pro_payable_type: "Milestone"))
    end
  end

  filter :job_id, :integer, only: [:eq] do
    eq do |scope, value|
      scope.joins(sub_pro_pays: :sub_pro_pay_pro_payables).
        merge(SubProPayProPayable.where(pro_payable: value, pro_payable_type: "Job"))
    end
  end

  def base_scope
    super
  end
end
