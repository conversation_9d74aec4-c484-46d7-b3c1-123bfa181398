# frozen_string_literal: true

class RouteResource < ApplicationResource
  belongs_to :organization, readable: true, writable: false, always_include_resource_ids: true
  belongs_to :branch, readable: true, writable: false, always_include_resource_ids: true
  belongs_to :manager, readable: true, writable: false, always_include_resource_ids: true, resource: IdentityResource
  belongs_to :crew_lead, readable: true, writable: false, always_include_resource_ids: true, resource: IdentityResource

  attribute :name, :string, readable: true, writable: false
  attribute :create_grouped_pro_pays, :boolean, readable: true, writable: true
end
