# frozen_string_literal: true

class AspireUserWithRolesResource < ApplicationResource
  self.adapter = Graphiti::Adapters::Null

  attribute :identity_id, :integer
  attribute :name, :string
  attribute :email, :string
  attribute :phone_country_code, :string
  attribute :phone, :string
  attribute :contact_id, :string
  attribute :aspire_roles, :array
  attribute :protiv_roles, :array
  attribute :has_user, :boolean
  attribute :user_id, :integer

  primary_endpoint "/integrations/aspire/aspire_users_with_roles", [:aspire_users_with_roles]

  # Add standard filters for email and phone
  filter :email, :string do
    eq do |scope, value|
      scope.select { |user| user.email == value }
    end

    null do |scope, value|
      if value
        scope.select { |user| user.email.nil? || user.email.empty? }
      else
        scope.select { |user| user.email.present? }
      end
    end
  end

  filter :phone, :string do
    eq do |scope, value|
      scope.select { |user| user.phone == value }
    end

    null do |scope, value|
      if value
        scope.select { |user| user.phone.nil? || user.phone.empty? || user.phone_country_code.nil? || user.phone_country_code.empty? }
      else
        scope.select { |user| user.phone.present? }
      end
    end
  end

  filter :protiv_role, :string, only: [:eq] do
    eq do |scope, value|
      scope.select { |user| user.protiv_roles.include?(value.first) }
    end
  end

  filter :identity_id, :integer, only: [:eq] do
    eq do |scope, value|
      scope.select { |user| user.identity_id == value }
    end
  end

  def base_scope
    organization = current_organization
    return [] unless organization

    integration = Integration.find_by(
      organization: organization,
      source: :aspire
    )
    return [] unless integration

    role_mapper = AspireRoleMapper.new(integration: integration)
    result = role_mapper.cached_sync_user_roles
    Rails.logger.info("Found #{result[:users_with_roles].size} users with roles")

    result[:users_with_roles].map do |user_data|
      AspireUserWithRoles.from_hash(user_data)
    end
  rescue => e
    Rails.logger.error(e.backtrace.join("\n"))
    []
  end
end
