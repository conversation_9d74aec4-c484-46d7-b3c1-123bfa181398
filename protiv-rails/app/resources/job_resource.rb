# frozen_string_literal: true

class JobResource < ApplicationResource
  FILTER_INTERVALS = {
    "7d" => 7.days,
    "14d" => 14.days,
    "30d" => 30.days,
    "60d" => 60.days,
    "90d" => 90.days
  }.freeze

  belongs_to :organization
  attribute :reference_name, :string, readable: true, writable: false
  attribute :name, :string, readable: true, writable: true
  attribute :client_name, :string
  attribute :created_date, :datetime
  attribute :last_activity_at, :datetime
  attribute :job_number, :string

  # polymorphic_has_one :sub_pro_pay_pro_payable, as: :pro_payable, always_include_resource_ids: true

  belongs_to :manager, always_include_resource_ids: true, resource: IdentityResource

  belongs_to :branch, always_include_resource_ids: true

  attribute :status, :string_enum, allow: Job.statuses.values, filterable: true
  attribute :progress, :hash
  attribute :budget, :hash

  attribute :total_budget, :money
  attribute :total_costs, :money
  attribute :seconds_budget, :integer
  attribute :seconds_cost, :integer

  attribute :milestone_count, :integer do
    @object.milestones.count
  end

  attribute :has_pro_pay, :boolean do
    @object.sub_pro_pay_pro_payable.present?
  end


  has_one :details, resource: JobDetailsResource do
    link do |job|
      Rails.application.routes.url_helpers.api_job_detail_path(job.to_param, include: "milestones.resolved_pro_pay,milestones.milestone_times.identity,property,branch")
    end
  end

  # alternatively, `recent` can be modelled as a custom filter operation on
  # `last_activity_at`, but modelling it this way gives you the `single` and
  # `allow` options which seems useful
  filter :recent, :string, single: true, only: [:eq], allow: FILTER_INTERVALS.keys do
    eq do |scope, value|
      interval = FILTER_INTERVALS.fetch(value)
      scope.where(last_activity_at: interval.ago..)
    end
  end

  filter :manager_id, :integer, only: [:eq] do |scope, values|
    scope.where(manager_id: values)
  end

  filter :is_recurring, :boolean, single: true do |scope, value|
    scope.where(job_type: Job.job_types["recurring"])
  end

  def base_scope
    super.includes(:branch, :job_budgets, :manager, :milestones)
  end
end
