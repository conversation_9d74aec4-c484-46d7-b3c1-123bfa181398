# frozen_string_literal: true

class JobDetailsResource < ApplicationResource
  def self.model = Job

  # FIXME: Aspire -> PropertyID -> Property -> AccountOwnerContactID?
  attribute :contract_price, :money

  ApplicationRecord::BUDGET_CATEGORIES.each do |category|
    attribute :"#{category}_budget", :money
    attribute :"#{category}_cost", :money
  end
  attribute :gross_profit, :money
  attribute :budgeted_gross_profit, :money
  # FIXME: new model, sync WorkTicketVisits?
  # has_many :scheduled_visits

  has_many :milestones, always_include_resource_ids: true
  belongs_to :property, always_include_resource_ids: true
  attribute :property_id, :integer

  belongs_to :branch, always_include_resource_ids: true
  attribute :branch_id, :integer

  attribute :division_name, :string
  attribute :job_type, :string
  polymorphic_has_one :sub_pro_pay_pro_payable, as: :pro_payable, resource: SubProPayPayableResource

  has_one :resolved_sub_pro_pay, foreign_key: :job_id, resource: SubProPayResource  do
    pre_load do |proxy, _jobs|
      proxy.scope.object = proxy.scope.object.eager_load(:sub_pro_pay_pro_payables)
    end

    assign_each do |job, sub_pro_pays|
      selected = []
      sub_pro_pays.each do |spp|
        spp.sub_pro_pay_pro_payables.each do |sppp|
          selected << spp if sppp.pro_payable == job
        end
      end
      selected
    end
  end

  has_one :resolved_pro_pay, foreign_key: :job_id, resource: ProPayResource do
    pre_load do |proxy, _jobs|
      proxy.scope.object = proxy.scope.object.eager_load(sub_pro_pays: :sub_pro_pay_pro_payables)
    end

    assign_each do |job, pro_pays|
      selected = []
      pro_pays.each do |pp|
        pp.sub_pro_pays.each do |spp|
          spp.sub_pro_pay_pro_payables.each do |sppp|
            selected << pp if sppp.pro_payable == job
          end
        end
      end
      selected
    end
  end
end
