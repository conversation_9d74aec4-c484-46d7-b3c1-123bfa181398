# frozen_string_literal: true

class ApplicationResource < Graphiti::Resource
  include Pundit::Authorization
  # self.validate_endpoints = false # FIXME: unclear if we should want to do endpoint validation

  self.abstract_class = true
  self.adapter = Graphiti::Adapters::ActiveRecord
  self.autolink = false

  self.attributes_writable_by_default = false
  self.relationships_writable_by_default = false
  self.endpoint_namespace = "/api/v2"

  self.max_page_size = 2000
  self.default_page_size = 2000

  paginate do |scope, current_page, per_page|
    if scope.respond_to?(:page)
      scope.page(current_page).per(per_page)
    else
      scope
    end
  end

  # override built-in method to add `dasherize` and drop `pluralize`
  def self.infer_type
    if name.present?
      name.demodulize.sub(/.*\KResource/, "").underscore.dasherize.to_sym
    else
      :"undefined-jsonapi-type"
    end
  end

  def current_user
    context.current_user
  end

  def current_organization
    context.current_organization
  end

  def base_scope
    policy_scope(model)
  end

  before_save(only: :create) do |record|
    authorize(record, :create?)
  end

  before_save(only: :update) do |record|
    authorize(record, :update?) if record.changed?
  end

  before_destroy { |record| authorize(record, :destroy?) }
end
