# frozen_string_literal: true

class MilestoneTimeResource < ApplicationResource
  belongs_to :milestone, always_include_resource_ids: true
  attribute :milestone_id, :integer, only: [:filterable]
  attribute :start_time, :datetime
  attribute :end_time, :datetime
  attribute :duration_seconds, :integer
  attribute :base_hourly_rate, :money
  attribute :labor_cost, :money

  belongs_to :identity, always_include_resource_ids: true
  attribute :identity_id, :integer, only: [:filterable]
  filter :job_id, :integer do
    eq do |scope, value|
      scope.joins(milestone: :job).where(milestones: { job_id: value })
    end
  end

  def base_scope
    super.includes(:identity)
  end
end
