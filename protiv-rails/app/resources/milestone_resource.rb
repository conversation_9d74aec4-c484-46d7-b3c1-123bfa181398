# frozen_string_literal: true

class MilestoneResource < ApplicationResource
  belongs_to :job, filter: true, always_include_resource_ids: true
  attribute :job_id, :integer, filter: true, readable: false
  polymorphic_has_one :sub_pro_pay_pro_payable, as: :pro_payable, resource: SubProPayPayableResource

  has_one :resolved_sub_pro_pay, foreign_key: :milestone_id, resource: SubProPayResource do
    pre_load do |proxy, _milestones|
      proxy.scope.object = proxy.scope.object.eager_load(:sub_pro_pay_pro_payables)
    end

    assign_each do |milestone, sub_pro_pays|
      selected = []
      sub_pro_pays.each do |spp|
        spp.sub_pro_pay_pro_payables.each do |sppp|
          selected << spp if sppp.pro_payable == milestone
        end
      end
      selected
    end
  end

  has_one :resolved_pro_pay, foreign_key: :milestone_id, resource: ProPayResource do
    pre_load do |proxy, _milestones|
      proxy.scope.object = proxy.scope.object.eager_load(sub_pro_pays: :sub_pro_pay_pro_payables)
    end

    assign_each do |milestone, pro_pays|
      selected = []
      pro_pays.each do |pp|
        pp.sub_pro_pays.each do |spp|
          spp.sub_pro_pay_pro_payables.each do |sppp|
            selected << pp if sppp.pro_payable == milestone
          end
        end
      end
      selected
    end
  end

  has_many :milestone_times
  has_many :employee_milestone_times

  attribute :name, :string
  attribute :description, :string
  attribute :status, :string
  attribute :contract_price, :money
  attribute :seconds_budget, :integer
  attribute :seconds_cost, :integer
  ApplicationRecord::BUDGET_CATEGORIES.each do |category|
    attribute :"#{category}_budget", :money
    attribute :"#{category}_cost", :money
  end
  attribute :first_check_in, :datetime
  attribute :last_check_out, :datetime
  attribute :total_costs, :money
  attribute :total_budget, :money
  attribute :budget, :hash
  attribute :employee_count, :integer
  attribute :percent_complete, :float

  has_one :tracking_material, resource: TrackingMaterialResource

  def base_scope
    policy_scope(model).includes(:milestone_times, :job, :pro_pay)
  end
end
