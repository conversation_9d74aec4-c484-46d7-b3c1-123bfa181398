# frozen_string_literal: true

class EmployeeMilestoneTimeResource < ApplicationResource
  belongs_to :milestone, always_include_resource_ids: true
  attribute :milestone_id, :integer, only: [:filterable]
  attribute :duration_seconds, :integer
  attribute :start_time, :datetime
  attribute :end_time, :datetime
  attribute :base_hourly_rate, :money
  attribute :labor_cost, :money

  belongs_to :identity, always_include_resource_ids: true
  attribute :identity_id, :integer, only: [:filterable]

  def base_scope
    super.includes(:milestone, :identity)
  end
end
