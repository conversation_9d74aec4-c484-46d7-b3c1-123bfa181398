# frozen_string_literal: true

class  OrganizationUserResource < ApplicationResource
  self.validate_endpoints = false
  self.model = Role

  belongs_to :organization, readable: true, writable: false, always_include_resource_ids: true
  belongs_to :user, readable: true, writable: false, always_include_resource_ids: true

  # Basic attributes
  attribute :id, :integer, readable: true, writable: false
  attribute :role_type, :string, readable: true, writable: true
  attribute :active, :boolean, readable: true, writable: true
  attribute :status, :string, readable: true, writable: true
  attribute :active_at, :datetime, readable: true, writable: false
  attribute :created_at, :datetime, readable: true, writable: false
  attribute :updated_at, :datetime, readable: true, writable: false
  attribute :wage, :float, readable: true, writable: true

  # User attributes (delegated)
  attribute :user_name, :string, readable: true, writable: true do
    @object.user.name
  end
  attribute :user_email, :string, readable: true, writable: true do
    @object.user.email
  end
  attribute :user_phone_country_code, :string, readable: true, writable: true do
    @object.user.phone_country_code
  end
  attribute :user_phone, :string, readable: true, writable: true do
    @object.user.phone
  end

  attribute :user_invited, :boolean, readable: true, writable: false do
    @object.status == "invited" || (@object.active? && !@object.user.verified?)
  end

  attribute :user_verified, :boolean, readable: true, writable: false do
    @object.user.verified?
  end

  filter :organization_id, :integer
  filter :role_type, :string, allow: %w[admin manager crew_lead employee] do
    eq do |scope, value|
      scope.where(role_type: value)
    end
  end
  filter :active, :boolean

  filter :status, :string, allow: %w[active inactive invited uninvited] do
    eq do |scope, value|
      scope.where(status: value)
    end
  end

  def base_scope
    Role.unscoped.includes(:user, :organization).where(organization: Current.organization)
  end
end
