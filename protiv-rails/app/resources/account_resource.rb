# frozen_string_literal: true

class AccountResource < ApplicationResource
  class Adapter < Graphiti::Adapters::Null
    def filter_integer_eq(scope, attribute, value)
      raise "unsupported attribute #{attribute}" unless attribute == :id
      scope.select { |acct| acct.id == value }
    end
  end

  self.adapter = Adapter

  attribute :name, :string, writable: true, readable: true
  attribute :email, :string, writable: true, readable: true
  attribute :password, :string, writable: true, readable: false
  attribute :invitation_token, :string, writable: true, readable: false
  # attribute :current_organization, writable: false

  has_one :user, readable: true, writable: false, always_include_resource_ids: true do
    params do |hash, accounts|
      hash[:filter][:id] = hash[:filter].delete(:account_id)
    end

    assign do |accounts, users|
      users[0]
    end
  end

  belongs_to :organization, readable: true, writable: false, always_include_resource_ids: true

  has_many :organizations, readable: true, writable: false, always_include_resource_ids: true do
    params do |hash, accounts|
      # FIXME: this is silly
      hash[:filter].delete(:account_id)
      hash[:filter][:id] = accounts[0].organizations.pluck(:id)
    end

    assign do |accounts, organizations|
      organizations
    end
  end

  paginate do |scope, *args|
    scope
  end

  filter :id, only: :eq, single: true

  def save(account)
    account.build_and_validate_user

    if account.errors.none?
      account.create_user!
    end

    account
  end

  def base_scope
    policy_scope(self.model)
  end
end
