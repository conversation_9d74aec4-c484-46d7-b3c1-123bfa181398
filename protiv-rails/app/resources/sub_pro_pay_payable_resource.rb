# frozen_string_literal: true

class SubProPayPayableResource < ApplicationResource
  belongs_to :sub_pro_pay

  polymorphic_belongs_to :pro_payable do
    group_by(:pro_payable_type) do
      on(:Job)
      on(:Milestone)
    end
  end

  attribute :sub_pro_pay_id, :integer, only: [:filterable]
  attribute :pro_payable_id, :integer, only: [:filterable]
  attribute :pro_payable_type, :string, only: [:filterable]

  filter :sub_pro_pay_id
  filter :pro_payable_id
  filter :pro_payable_type, :string, allow: %w[Job Milestone]

  def base_scope
    super
  end
end
