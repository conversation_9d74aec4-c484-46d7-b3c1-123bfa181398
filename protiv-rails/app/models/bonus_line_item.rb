# frozen_string_literal: true

class BonusLineItem < ApplicationRecord
  belongs_to :partial_sub_pro_pay
  belongs_to :payable, polymorphic: true

  enum :bonus_source, {
    company: "company",
    rounding: "rounding",
    crew_lead: "crew_lead",
    manager: "manager",
    other: "other",
    crew: "crew",
    crew_retention: "crew_retention"
  }

  monetize :labor_cost_cents
  monetize :bonus_cents
end
