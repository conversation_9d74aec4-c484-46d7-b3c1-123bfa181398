# frozen_string_literal: true

class Route < ApplicationRecord
  # FIXME: organization should not be optional. Fix after we know staging has been updated with organization
  belongs_to :organization, optional: true
  belongs_to :branch, optional: true
  belongs_to :crew_lead, class_name: "Identity"
  belongs_to :manager, optional: true, class_name: "Identity"
  trackable

  # FIXME: validate relationships are correct for organization
end
