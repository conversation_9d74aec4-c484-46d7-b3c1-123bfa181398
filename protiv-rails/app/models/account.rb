# frozen_string_literal: true

class Account
  include ActiveModel::Model
  include ActiveModel::Attributes
  include ActiveModel::Validations

  attribute :email, :string
  attribute :name, :string
  attribute :password, :string
  attribute :invitation_token, :string
  attr_accessor :user, :organization

  validate :validate_user

  def attributes
    { email: nil, name: nil, password: nil, invitation_token: nil }
  end

  def self.current
    new.tap do |account|
      account.user = Current.user
      account.organization = Current.organization
    end
  end

  def email
    super || user&.email
  end

  def name
    super || user&.name
  end

  def build_and_validate_user
    self.user = User.build(email: email, password: password, password_confirmation: password, name: name)
    validate
  end

  def create_user!
    ApplicationRecord.transaction do
      user.save!

      if invitation_token.present?
        invitation = Invitation.active.find_by_token_for(:account_create, invitation_token)
        invitation&.claim!(user)
      end
    end
  end

  def id
    user&.id
  end

  def errors
    user.errors
  end

  def validate_user
    user&.validate
  end

  def organizations
    user&.organizations || Organization.none
  end
end
