# frozen_string_literal: true

require "range_utils"

class Integration < ApplicationRecord
  belongs_to :organization

  has_one :credential
  has_many :changesets
  has_many :sync_statuses
  has_many :sync_status_ranges
  has_many :sync_caches, class_name: "Sync::Cache"
  has_many :sync_data

  has_many :integration_records

  encrypts :client_id
  encrypts :secret

  has_many :jobs, through: :integration_records, source: :record, source_type: "Job"
  has_many :routes, through: :integration_records, source: :record, source_type: "Route"
  has_many :identities, through: :integration_records, source: :record, source_type: "Identity"
  has_many :branches, through: :integration_records, source: :record, source_type: "Branch"
  has_many :properties, through: :integration_records, source: :record, source_type: "Property"
  has_many :clock_times, through: :integration_records, source: :record, source_type: "ClockTime"
  has_many :milestones, through: :integration_records, source: :record, source_type: "Milestone"
  has_many :milestone_times, through: :integration_records, source: :record, source_type: "MilestoneTime"
  has_many :milestone_items, through: :integration_records, source: :record, source_type: "MilestoneItem"
  has_many :services, through: :integration_records, source: :record, source_type: "Service"
  has_many :catalog_items, through: :integration_records, source: :record, source_type: "CatalogItem"
  has_many :item_allocations, through: :integration_records, source: :record, source_type: "ItemAllocation"
  has_many :crew_leads, through: :routes

  include RangeUtils

  validates :client_id, presence: true, if: :requires_client_id?
  validates :secret, presence: true, if: :requires_secret?
  validates :sync_interval, numericality: { in: (1.hour..24.hours) }

  scope :aspire, -> { where(source: :aspire) }

  around_save :invalidate_credentials, unless: :new_record?

  validate :organization_id_cannot_change, unless: :new_record?

  scope :needs_sync, -> {
    where(initial_sync_done: true, auto_sync: true) \
    .where("(integrations.last_synced_at + integrations.sync_interval) < CURRENT_TIMESTAMP")
  }

  scope :needs_initial_sync, -> {
    where(initial_sync_done: false, auto_sync: true)
  }

  delegate \
    :refresh_credential,
    :maybe_refresh_credential,
    to: :adapter

  def adapter
    @adapter ||= sync_module.adapter.new(self)
  end

  def cache_adapter
    @cache_adapter ||= Sync::CacheAdapter.new(adapter)
  end

  def cache_all
    cache_adapter.cache_all
  end

  def materialize_all
    materializer.materialize_all
  end

  def materializer
    @materializer ||= sync_module.materializer.new(self)
  end

  def sync_module
    Sync.lookup(source)
  end

  def initial_sync_percent_complete
    statuses = jobs.group(:initial_sync_done).count
    total = statuses.fetch(false, 0) + statuses.fetch(true, 0)

    if total.zero?
      0
    else
      statuses.fetch(true, 0) / total.to_f
    end
  end

  # @param from_cache_only [Boolean] when true, only sync changes from the cache (do not call remote APIs)
  def sync_all(async: true)
    if async
      Sync::SyncIntegrationJob.perform_async(id)
    else
      update!(last_sync_started_at: Time.now.utc)

      Sync::Current.track do
        cache_all
        materialize_all
      end

      update!(last_synced_at: last_sync_started_at)
    end
  end

  def initial_resource_sync_done?(resource)
    sync_statuses.where(resource:).initially_synced.exists?
  end

  def initial_resource_materialization_done?(resource)
    sync_statuses.where(resource: "materialize_#{resource}").initially_synced.exists?
  end

  # FIXME: this should introspect the initial sync stages on the adapter.
  def check_initial_sync_complete!
    return true if initial_sync_done
    return unless initial_resource_sync_done?(:branches)
    return unless initial_resource_sync_done?(:identities)
    return unless initial_resource_sync_done?(:routes)
    return if jobs.none?
    return if jobs.where(initial_sync_done: false).exists?

    transaction do
      statuses_to_update = %i[jobs milestones milestone_times milestone_items item_allocations].flat_map do |resource|
        [
          sync_statuses.where(resource:).first_or_initialize,
          sync_statuses.where(resource: "materialize_#{resource}").first_or_initialize
        ]
      end

      statuses_to_update.each do |status|
        status.last_sync_started_at = created_at
        status.last_synced_at = created_at
      end

      statuses_to_update.each do |status|
        status.save if status.changed?
      end
    end

    # FIXME: this assumes every adapter uses ranged sync for clock times
    clock_statuses = sync_status_ranges.where(resource: :clock_times).pluck(:last_synced_at)
    return if clock_statuses.empty? || clock_statuses.any?(&:blank?)

    status = sync_statuses.where(resource: :clock_times).first_or_initialize
    status.last_sync_started_at = created_at
    status.last_synced_at = created_at

    status.save

    # Mark remote helpers as initially synced.
    # This implies that they should have been downloaded as dependencies
    # in an earlier step; we'll track the status here to get subsequent updates.
    adapter.remote_helper_models.each do |remote_resource|
      status = sync_statuses.where(resource: remote_resource).first_or_initialize
      status.helper_model = true
      status.last_sync_started_at = created_at
      status.last_synced_at = created_at
    end

    update(initial_sync_done: true)
  end

  def cache_clock_time_range(sync_status_range, **options)
    cache_adapter.cache_clock_time_ranges(sync_status_range:, **options)
  end

  def ensure_clock_time_ranges!
    transaction do
      if sync_status_ranges.where(resource: :clock_times).empty?
        # first segment should be for current month
        t0 = Time.now.utc.beginning_of_day + 1.month

        13.times do
          sync_status_ranges.create(
            resource: :clock_times,
            end_timestamp: t0,
            start_timestamp: t0 -= 1.month,
          )
        end
      end
    end


    max_date = sync_status_ranges.where(resource: :clock_times).maximum(:end_timestamp) - 5.minutes

    now = Time.now.utc
    if max_date < now
      ensure_clock_time_ranges(date_range: max_date..Time.now.utc)
    end

    ensure_current_clock_time_range
  end

  def ensure_current_clock_time_range
    t = Time.now.utc
    ensure_clock_time_ranges(date_range: (t..t))
  end

  def ensure_clock_time_ranges(date_range:)
    ranges = sync_status_ranges.where(resource: :clock_times).map(&:date_range)
    unsynced_ranges = diff_ranges(date_range, ranges)

    return true if unsynced_ranges.empty?

    mday = ranges.empty? ? 1 : ranges.first.first.mday

    aligned_ranges = []

    unsynced_ranges.each do |range|
      a = range.first.utc.beginning_of_day
      target_end = (range.last.utc + 1.day).beginning_of_day

      a -= 1.day until a.mday == mday
      b = a + 1.month

      loop do
        aligned_ranges << (a..b)
        break if b >= target_end

        a = b
        b = a + 1.month
      end
    end

    aligned_ranges.uniq.each do |aligned_range|
      status_range = sync_status_ranges.where(
        resource: "clock_times",
        start_timestamp: aligned_range.first,
        end_timestamp: aligned_range.last,
      ).first_or_create!

      yield status_range if block_given?
    end
  end

  def ensure_clock_times(date_range:)
    ensure_clock_time_ranges(date_range:) do |status_range|
      sync_clock_time_range(status_range)
    end
  end

  def sync_clock_time_range(status_range, async = true)
    unless status_range.resource.to_s == "clock_times"
      raise ArgumentError, "incorrect resource #{status_range.resource}"
    end

    if async
      Sync::SyncClockTimeRangeJob.perform_async(status_range.id)
    else
      Sync::SyncClockTimeRangeJob.new.perform(status_range.id)
    end
  end

  # sync_status may be
  # - a SyncStatus
  # - a SyncStatusRange
  # - true - implicitly maps to the resource's SyncStatus
  # - false - no status will be tracked
  def tracking_sync(resource, sync_status: nil, **opts)
    Sync::ErrorTracking.track(integration_id: id) do
      skip_status = sync_status == false
      last_synced_at = nil

      unless skip_status
        sync_status = nil if sync_status == true
        if sync_status
          if sync_status.integration_id != id
            raise ArgumentError, "sync_status must belong to this integration"
          end
        end

        sync_status ||= sync_statuses.where(resource:).first_or_initialize

        last_synced_at = sync_status.last_synced_at

        # Always set this to the current time.
        sync_status.last_sync_started_at = Time.zone.now
        sync_status.save
      end

      # FIXME: lock the sync sync_status row as a general mutex?

      payload = { integration_id: id, sync_resource: resource }

      ActiveSupport::Notifications.instrument("tracking_sync.integration", payload) do |payload|
        Sync::Current.track(integration_id: id, **opts) do
          payload[:sync_uuid] = Sync::Current.uuid
          yield sync_status
        end
      end.tap do
        unless skip_status || sync_status.skip
          # `last_synced_at` should be set to the time when the sync started,
          # and will ONLY be set if the sync succeeded.
          #
          # The subsequent sync will use this attribute as the cursor of where to start
          # the next sync
          sync_status.last_synced_at = sync_status.last_sync_started_at
          sync_status.save
        end
      end
    end
  end

  def aspire?
    source == Sync::Aspire.source
  end

  def maybe_create_sync_datum(record)
    should_create = case record
    when Attendance
      sync_module.can_create?(:clock_times) || sync_module.can_create?(:milestone_times)
    else
      raise "unknown sync datum type #{record.class}"
    end

    if should_create
      datum = sync_data.create(record:)
      Sync::SyncDatumJob.perform_async(datum.id)
    end
  end

  # Should return a hash to identify a resource, e.g.
  # { source: 'aspire', remote_resource: 'clock_time', remote_id: 123 },
  # or nil
  def sync_to_remote!(record)
    case record
    when Attendance
      adapter.create_clock_time(record)
    end
  end

  def backsync_datum(remote_resource:, remote_id:, **)
    cache = sync_caches.where(source:, remote_resource:, remote_primary_id: remote_id).first_or_create!
    caches = cache_adapter.fetch_placeholders([cache])
    materializer.materialize_caches(caches)
  end

  private

  # FIXME: move to sync mod
  def requires_client_id?
  end

  # FIXME: move to sync mod
  def requires_secret?
    aspire?
  end

  def invalidate_credentials
    if client_id_changed? || secret_changed?
      transaction do
        credential&.destroy
        yield
      end
    else
      yield
    end
  end

  # NOTE: using attr_readonly does not work here
  # because it doesn't inspect changes (i.e. including the current id
  # in an update payload _will_ raise a readonly error)
  def organization_id_cannot_change
    if organization_id_changed?
      errors.add(:organization, "cannot save change to organization")
    end
  end
end
