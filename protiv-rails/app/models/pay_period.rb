# frozen_string_literal: true

class PayPeriod < ApplicationRecord
  belongs_to :payroll_schedule
  belongs_to :branch

  scope :current, -> { for_time(Time.now) }
  scope :for_time, ->(time) { where("? BETWEEN pay_periods.start_time AND pay_periods.end_time", time) }
  scope :future, -> { where("start_time > ?", Time.now) }

  enum :period_type, {
    pay: "pay",
    bonus: "bonus",
    route: "route"
  }, validate: true

  validates :start_time, presence: true
  validates :end_time, presence: true
  validates :period_type, presence: true
  validate :no_overlap_on_create, on: :create
  validate :no_gap_on_create, on: :create
  validate :no_gaps_or_overlaps_introduced, on: :update
  validate :range_is_valid

  before_destroy :check_for_gaps_before_destroy

  private

  # PayPeriods must form a contiguous block. New PayPeriods may only be added to the ends of the block
  def no_overlap_on_create
    return unless branch&.persisted? && period_type.present? && start_time.present? && end_time.present?

    pay_periods_table = PayPeriod.arel_table

    # Subqueries for minimum start_time and maximum end_time
    min_start_time = PayPeriod.where(branch:, period_type:).select("MIN(start_time)")
    max_end_time = PayPeriod.where(branch:, period_type:).select("MAX(end_time)")

    # Arel condition for overlaps
    overlaps_condition = Arel::Nodes::InfixOperation.new(
      "OVERLAPS",
      Arel::Nodes::Grouping.new(
        Arel::Nodes::SqlLiteral.new("(#{min_start_time.to_sql}), (#{max_end_time.to_sql})")
      ),
      Arel::Nodes::Grouping.new(
        Arel::Nodes::SqlLiteral.new("#{ActiveRecord::Base.connection.quote(self.start_time)}, #{ActiveRecord::Base.connection.quote(self.end_time)}")
      )
    )

    # Query to retrieve PayPeriods with overlap
    conflicts = PayPeriod.where(pay_periods_table[:branch_id].eq(self.branch_id))
                         .where(pay_periods_table[:period_type].eq(self.period_type))
                         .where(overlaps_condition)

    if conflicts.exists?
      errors.add(:base, "Pay Periods cannot overlap with existing periods")
    end
  end

  def no_gap_on_create
    return unless branch&.persisted? && period_type.present? && start_time.present? && end_time.present?

    min_start_time, max_end_time = PayPeriod.where(branch:, period_type:)
                                            .pluck("MIN(start_time)", "MAX(end_time)").first

    # There can be no gap if this is the first PayPeriod
    return if min_start_time.nil? && max_end_time.nil?

    if !(self.end_time == min_start_time || self.start_time == max_end_time)
      errors.add(:base, "Pay Periods must not have gaps")
    end
  end

  def no_gaps_or_overlaps_introduced
    # Note, we're not using a window function since it won't pick up the changes to the record, though we could add a
    # database constraint to enforce this

    # Changing branch or period type will move a pay_period out of sequence, introducing a gap
    # If the period is not in the middle it may be destroyed

    errors.add(:branch, "must not introduce gaps") if self.branch_changed?
    errors.add(:period_type, "must not introduce gaps") if self.period_type_changed?

    # If the end_time changes and there is a later entry it will introduce a gap or overlap
    if end_time_changed?
      if PayPeriod.where(branch:, period_type:).where.not(id:)
                  .where("start_time >= ?", end_time_was).exists?
        errors.add(:end_time, "must not introduce gaps or overlaps")
      end
    end

    # If the start_time changes and there is an earlier entry it will introduce a gap or overlap
    if start_time_changed?
      if PayPeriod.where(branch:, period_type:).where.not(id:)
                  .where("end_time <= ?", self.start_time_was).exists?
        errors.add(:start_time, "must not introduce gaps or overlaps")
      end
    end
  end

  def range_is_valid
    if start_time > end_time
      errors.add(:base, "start_time cannot exceed end_time")
    end
  end

  def check_for_gaps_before_destroy
    # FIXME: Handle this with one database query
    before_exists = PayPeriod.where(branch:, period_type:).where.not(id:)
                             .where("end_time <= ?", self.start_time).exists?
    after_exists = PayPeriod.where(branch:, period_type:).where.not(id:)
                            .where("start_time >= ?", self.end_time).exists?

    throw :abort if before_exists && after_exists
  end
end
