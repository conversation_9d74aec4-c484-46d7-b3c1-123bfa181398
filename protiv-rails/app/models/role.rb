# frozen_string_literal: true

class Role < ApplicationRecord
  belongs_to :user
  belongs_to :organization

  ADMIN = "admin"
  MANAGER = "manager"
  CREW_LEADER = "crew_lead"
  EMPLOYEE = "employee"

  STATUS_ACTIVE = "active"
  STATUS_INACTIVE = "inactive"
  STATUS_UNINVITED = "uninvited"
  STATUS_INVITED = "invited"

  scope :active, -> { where(active: true) }
  default_scope { active }

  scope :inactive, -> { where(active: false) }
  scope :invited, -> { where(status: :invited) }
  scope :uninvited, -> { where(status: :uninvited) }

  scope :admin, -> { where(role_type: ADMIN) }
  scope :manager, -> { where(role_type: MANAGER) }
  scope :crew_leader, -> { where(role_type: CREW_LEADER) }
  scope :employee, -> { where(role_type: EMPLOYEE) }

  scope :with_status_active, -> { where(status: STATUS_ACTIVE) }
  scope :with_status_inactive, -> { where(status: STATUS_INACTIVE) }
  scope :with_status_uninvited, -> { where(status: STATUS_UNINVITED) }
  scope :with_status_invited, -> { where(status: STATUS_INVITED) }

  enum :role_type, { admin: ADMIN, manager: MANAGER, crew_leader: CREW_LEADER, employee: EMPLOYEE }
  enum :status, { active: STATUS_ACTIVE, inactive: STATUS_INACTIVE, uninvited: STATUS_UNINVITED, invited: STATUS_INVITED }

  # Sync the active boolean with the status for backward compatibility
  before_save :sync_active_with_status

  # Status helper methods
  def make_active!
    update!(status: STATUS_ACTIVE, active: true, active_at: Time.zone.now)
  end

  def make_inactive!
    update!(status: STATUS_INACTIVE, active: false, inactive_at: Time.zone.now)
  end

  def make_invited!
    update!(status: STATUS_INVITED, active: true, active_at: Time.zone.now)
  end

  def make_uninvited!
    update!(status: STATUS_UNINVITED, active: true, active_at: Time.zone.now)
  end

  private

  def sync_active_with_status
    if status_changed?
      self.active = (status == STATUS_ACTIVE || status == STATUS_INVITED || status == STATUS_UNINVITED)
      self.active_at = Time.zone.now if active && (active_changed? || active_at.nil?)
      self.inactive_at = Time.zone.now if !active && (active_changed? || inactive_at.nil?)
    end
  end
end
