# frozen_string_literal: true

class Milestone < ApplicationRecord
  belongs_to :job, optional: true
  has_one :organization, through: :job
  belongs_to :manager, required: false, class_name: "Identity"
  belongs_to :crew_lead, required: false, class_name: "Identity"
  belongs_to :service, optional: true
  has_one :sub_pro_pay_pro_payable, as: :pro_payable
  has_one :sub_pro_pay, through: :sub_pro_pay_pro_payable
  has_one :pro_pay, through: :sub_pro_pay
  has_many :item_allocations # usage
  has_many :milestone_items # budgeted
  belongs_to :tracked_milestone_item, optional: true, class_name: "MilestoneItem"
  has_many :catalog_items, through: :milestone_items
  has_many :allocated_catalog_items, through: :item_allocations, source: :catalog_item

  validate :currency_matches_job
  validate :tracked_milestone_item_belongs_to_milestone

  # Needed for Hopping Relationships, see https://www.graphiti.dev/cookbooks/hopping-relationships
  attr_accessor :resolved_pro_pay
  attr_accessor :resolved_sub_pro_pay

  has_many :milestone_times
  has_many :routes, through: :milestone_times

  scope :with_sub_pro_pay, -> do
    join_sql = <<~SQL
        INNER JOIN sub_pro_pay_pro_payables sub_pro_pay_pro_payables ON\
        (sub_pro_pay_pro_payables.pro_payable_type = 'Job' AND \
        sub_pro_pay_pro_payables.pro_payable_id = milestones.job_id) \
        OR (sub_pro_pay_pro_payables.pro_payable_type = 'Milestone' AND \
        sub_pro_pay_pro_payables.pro_payable_id = milestones.id)
      SQL

    joins(join_sql)
  end

  scope :without_sub_pro_pay, -> do
    join_sql = <<~SQL
        LEFT JOIN sub_pro_pay_pro_payables ON \
        (sub_pro_pay_pro_payables.pro_payable_type = 'Job' AND \
        sub_pro_pay_pro_payables.pro_payable_id = milestones.job_id) \
        OR (sub_pro_pay_pro_payables.pro_payable_type = 'Milestone' AND \
        sub_pro_pay_pro_payables.pro_payable_id = milestones.id)
      SQL

    joins(join_sql).where(sub_pro_pay_pro_payables: { id: nil })
  end

  scope :with_recurring_job, -> { joins(:job).where(jobs: { job_type: "recurring" }) }
  scope :with_non_recurring_job, -> { joins(:job).where(jobs: { job_type: "non_recurring" }) }

  before_save :maybe_update_percent_complete_cache
  after_commit :update_job_status

  def update_job_status
    if saved_change_to_percent_complete_cached_hundredths?
      job&.update_cache_columns_async
    end
  end

  # NOTE: This is an aggregated view of milestone_times by employee
  has_many :employee_milestone_times

  trackable

  # FIXME: Verify the correct enum values
  enum :status, {
    pending: "pending",
    in_progress: "in_progress",
    completed: "completed",
    canceled: "canceled"
  }

  BUDGET_CATEGORIES.each do |category|
    monetize :"#{category}_cost_cents", with_model_currency: :currency
    monetize :"#{category}_budget_cents", with_model_currency: :currency
  end

  monetize :contract_price_cents, with_model_currency: :currency

  def description
    # FIXME:
    "Milestone #{id}"
  end

  def first_check_in
    if association_cached?(:milestone_times)
      milestone_times.filter_map(&:start_time).min
    else
      milestone_times.minimum(:start_time)
    end
  end

  def last_check_out
    if association_cached?(:milestone_times)
      milestone_times.filter_map(&:end_time).max
    else
      milestone_times.maximum(:end_time)
    end
  end

  def total_costs
    equipment_cost + material_cost + labor_cost
  end

  def total_budget
    equipment_budget + material_budget + labor_budget
  end

  def budget
    unless total_budget.zero?
      {
        status: budget_status,
        percentage: budget_used_percent
      }
    end
  end

  def budget_used_ratio
    return 0.0 if total_budget.zero?
    total_costs.to_f / total_budget.to_f
  end

  # Ideally we would use stats from graphitti to get this kind of metadata but
  # it doesn't seem to work well when the employees are sideloaded
  def employee_count
    employee_milestone_times.count
  end

  # returns 0.0 .. 100.0 as a float
  def budget_used_percent
    (budget_used_ratio * 100).round(1)
  end

  def budget_status
    ratio = budget_used_ratio

    if ratio > 1
      "over"
    elsif ratio > 0.9
      "near"
    else
      "under"
    end
  end

  def percent_complete=(value)
    self.percent_complete_hundredths = (value * 100).to_i
    self.percent_complete_cached_hundredths = self.percent_complete_hundredths
  end

  def percent_complete
    percent_complete_cached_hundredths / 100.0
  end

  def percent_complete_uncached
    return 100.0 if completed?
    progress_tracking_source.percent_complete
  end

  def update_percent_complete_cached
    self.percent_complete_cached_hundredths = (percent_complete_uncached * 100).to_i
  end

  def update_cache_columns
    update_percent_complete_cached
    save if changed?
  end

  def update_cache_columns_async
    Cache::UpdateMilestoneCachesJob.perform_async(id)
  end

  def progress_tracking_source
    if percent_complete_hundredths.present?
      PercentComplete.new(percent_complete_hundredths)
    elsif tracked_catalog_item
      tracking_material
    else
      PercentComplete.new(0.0)
    end
  end

  def tracking_material
    return unless (catalog_item = tracked_catalog_item)
    return unless catalog_item.is_a?(CatalogItem)

    budgeted_items = milestone_items.where(catalog_item:)
    allocations = item_allocations.where(catalog_item:)

    TrackingMaterial.new(milestone: self, budgeted_items:, allocations:)
  end

  private

  def maybe_update_percent_complete_cache
    # NOTE: the cache update for manual percent complete happens in-line in the setter
    if tracked_milestone_item_id_changed? || status_changed?
      update_percent_complete_cached
    end
  end

  def tracked_catalog_item
    tracked_milestone_item&.catalog_item || service&.tracking_material
  end

  def currency_matches_job
    return unless job

    if currency != job.currency
      errors.add(:currency, "Currency #{currency} does not match job's currency (#{job.currency}), but should")
    end
  end

  def tracked_milestone_item_belongs_to_milestone
    if tracked_milestone_item && tracked_milestone_item.milestone_id != id
      errors.add(:tracked_milestone_item, "must belong to this milestone")
    end
  end
end
