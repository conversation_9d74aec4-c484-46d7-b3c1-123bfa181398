# frozen_string_literal: true

class Service < ApplicationRecord
  belongs_to :organization
  belongs_to :tracking_material, class_name: "CatalogItem", optional: true

  has_many :milestones
  has_many :catalog_items, through: :milestones
  after_commit :update_milestone_caches

  trackable

  def eligible_tracking_materials
    catalog_items.material
  end

  private

  def update_milestone_caches
    if saved_change_to_tracking_material_id?
      milestone_ids.each do |id|
        Cache::UpdateMilestoneCachesJob.perform_async(id)
      end
    end
  end
end
