# frozen_string_literal: true

class Invitation < ApplicationRecord
  belongs_to :organization
  belongs_to :user, optional: true
  belongs_to :invited_by, class_name: "User", optional: true # FIXME: should not be optional?
  enum :role_type, { admin: "admin", manager: "manager", crew_leader: "crew_lead", employee: "employee" }

  scope :active, -> () { where(user_id: nil, deleted_at: nil).where("expires_at > ?", Time.now) }

  before_create :set_expires_at

  # FIXME: unclear if these need to be different purposes
  generates_token_for(:account_create, expires_in: 7.days)
  generates_token_for(:add_user_to_organization, expires_in: 7.days)

  def set_expires_at
    self.expires_at ||= 1.week.from_now
  end

  class AlreadyClaimed < StandardError
  end

  class Expired < StandardError
  end

  def expired?
    expires_at < Time.zone.now
  end

  def claim!(user)
    raise AlreadyClaimed unless user_id.blank?
    raise Expired if expired?

    transaction(requires_new: false) do
      update(user_id: user.id)

      AddUserToOrganization.new(
        user: user,
        organization: organization,
        role_type: role_type
      ).execute!
    end
  end
end
