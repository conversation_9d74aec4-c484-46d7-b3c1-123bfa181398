# frozen_string_literal: true

class User < ApplicationRecord
  has_secure_password

  scope :verified, -> { where(verified: true) }

  generates_token_for :email_verification, expires_in: 2.days do
    email
  end

  generates_token_for :password_reset, expires_in: 20.minutes do
    password_salt.last(10)
  end

  generates_token_for :password_change_verification, expires_in: 20.minutes do
    "#{email}-#{password_digest.first(10)}"
  end

  generates_token_for :api do
    "#{id}-#{token_version}-#{password_salt.last(10)}"
  end

  # has_many :sessions, dependent: :destroy
  has_many :sign_in_tokens, dependent: :destroy
  has_many :events, dependent: :destroy

  has_many :roles
  has_many :inactive_roles, -> { inactive }, class_name: "Role"

  has_many :organizations, -> { distinct }, through: :roles
  has_many :employee_organizations, -> { where(roles: { role_type: :employee }) }, through: :roles, source: :organization
  has_many :crew_leader_organizations, -> { where(roles: { role_type: :crew_leader }) }, through: :roles, source: :organization
  has_many :manager_organizations, -> { where(roles: { role_type: :manager }) }, through: :roles, source: :organization
  has_many :admin_organizations, -> { where(roles: { role_type: :admin }) }, through: :roles, source: :organization
  has_many :integrations, through: :admin_organizations

  has_many :identities

  has_many :branches, through: :organizations # FIXME: narrow down to admin/manager roles
  has_many :routes, through: :organizations
  has_many :jobs, through: :organizations # FIXME: narrow down to admin/manager/crew roles
  has_many :properties, through: :organizations # FIXME: narrow down to admin/manager/crew roles
  has_many :pro_pays, through: :organizations # FIXME: narrow down to admin/manager/crew roles
  has_many :sub_pro_pays, through: :organizations # FIXME: narrow down to admin/manager/crew roles
  has_many :sub_pro_pay_pro_payables, through: :organizations # FIXME: narrow down to admin/manager/crew roles

  # FIXME: not sure this is going to be worth the added complexity
  has_one :primary_identity, -> { where(primary: true) }

  validates :name, presence: true, length: { maximum: 100 }
  validates :email, presence: true, uniqueness: true, format: { with: /\A[^@\s]+@[^@\s]+\.[^@\s]+\z/ }
  validates :password, allow_nil: true, length: { minimum: 8 }
  validates :phone_country_code, presence: true, length: { maximum: 4 }, if: -> { phone.present? }
  validates :phone, presence: true, length: { maximum: 20 }, if: -> { phone_country_code.present? }

  normalizes :email, with: -> { _1.strip.downcase }

  before_validation if: :email_changed?, on: :update do
    self.verified = false
  end

  # after_update if: :password_digest_previously_changed? do
  #   sessions.where.not(id: Current.session).delete_all
  # end

  after_update if: :email_previously_changed? do
    events.create! action: "email_verification_requested"
  end

  after_update if: :password_digest_previously_changed? do
    events.create! action: "password_changed"
  end

  after_update if: [:verified_previously_changed?, :verified?] do
    events.create! action: "email_verified"
  end

  def sign_in_token(expires_in: 1.day, organization_id: nil)
    sign_in_tokens.create(organization_id: organization_id).signed_id(expires_in: expires_in)
  end

  # Invalidates all sessions for the current user by incrementing the token_version
  # This forces the user to re-authenticate on their next request
  def invalidate_all_sessions!
    increment!(:token_version)
    events.create! action: "sessions_invalidated"
    true
  end

  def can_clock_in?(identity)
    identity.user_id == id || can_manage_organization?(identity.organization)
  end

  def can_manage_organization?(organization)
    roles.where(organization: organization, role_type: [Role::ADMIN, Role::MANAGER]).any?
  end
end
