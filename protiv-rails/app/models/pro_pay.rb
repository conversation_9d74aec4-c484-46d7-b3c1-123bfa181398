# frozen_string_literal: true

class ProPay < ApplicationRecord
  belongs_to :organization
  belongs_to :reference_bonus_pool, optional: true, class_name: "Bonus<PERSON>ool"
  belongs_to :source_route, optional: true, class_name: "Route"
  belongs_to :source_route_pay_period, optional: true, class_name: "<PERSON><PERSON>eri<PERSON>"
  belongs_to :source_job, optional: true, class_name: "Job"

  belongs_to :manager, optional: true, class_name: "Identity"

  has_many :sub_pro_pays
  has_many :bonus_line_items, through: :partial_pro_pays
  has_many :pro_pay_payables

  enum :source_type, {
    route_recurring_jobs: "route_recurring_jobs",
    route_non_recurring_jobs: "route_non_recurring_jobs",
    job_with_phases: "job_with_phases",
    manual: "manual"
  }

  attribute :reset_bonus_pool, :boolean, default: false

  validates :crew_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :manager_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :crew_lead_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :company_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :other_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validates :crew_retention_percent_hundredths,
            numericality: { only_integer: true, less_than_or_equal_to: 100_00, greater_than_or_equal_to: 0 }

  validate :totals_100_percent

  after_create :initialize_bonus_pool_values_and_payables
  before_update :reset_bonus_pool_values_and_payables

  def crew_percent
    crew_percent_hundredths.to_f / 100_00
  end

  def crew_retention_percent
    crew_retention_percent_hundredths.to_f / 100_00
  end

  def payable_percent(rate_class:)
    case rate_class
    when "manager"
      manager_percent_hundredths.to_f / 100_00
    when "crew_lead"
      crew_lead_percent_hundredths.to_f / 100_00
    when "company"
      company_percent_hundredths.to_f / 100_00
    when "other"
      other_percent_hundredths.to_f / 100_00
    end
  end

  private

  def totals_100_percent
    total_percent = crew_percent_hundredths + company_percent_hundredths + manager_percent_hundredths +
      crew_lead_percent_hundredths + other_percent_hundredths

    if total_percent != 100_00
      errors.add(:base, "percentages must total to 100%")
    end
  end

  def exclude_payable_others_from_bonus
    pro_pay_payable_others.each { |po| po.update(participating: false) }
  end

  def initialize_bonus_pool_payables(bonus_pool)
    # bonus_pool.bonus_pool_payables should all be of rate_class Other
    bonus_pool.bonus_pool_payables.each do |bonus_payable|
      ppp = ProPayPayable.find_by(pro_pay: self,
                                  payable: bonus_payable.payable,
                                  rate_class: bonus_payable.rate_class)
      if ppp
        ppp.update!(exclude_from_bonus: false) if ppp.exclude_from_bonus
      else
        ppp = ProPayPayable.create!(pro_pay: self,
                                    payable: bonus_payable.payable,
                                    rate_class: bonus_payable.rate_class)
        pro_pay_payables << ppp
      end
    end
  end

  def update_pool_values(bonus_pool)
    return unless bonus_pool

    self.company_percent_hundredths = bonus_pool.company_percent_hundredths
    self.crew_lead_percent_hundredths = bonus_pool.crew_lead_percent_hundredths
    self.manager_percent_hundredths = bonus_pool.manager_percent_hundredths
    self.other_percent_hundredths = bonus_pool.other_percent_hundredths
    self.crew_percent_hundredths = bonus_pool.crew_percent_hundredths
    self.crew_retention_percent_hundredths = bonus_pool.crew_retention_percent_hundredths
  end

  def initialize_bonus_pool_values_and_payables
    if reference_bonus_pool.present?
      initialize_bonus_pool_payables(reference_bonus_pool)
      update_pool_values(reference_bonus_pool)
    end
  end

  def reset_bonus_pool_values_and_payables
    if reference_bonus_pool_changed? || reset_bonus_pool
      exclude_payable_others_from_bonus
      initialize_bonus_pool_payables(reference_bonus_pool)

      # Updating pool values from reference before yielding to allow payload to further override the values
      update_pool_values(reference_bonus_pool)
    end
  end

  def pro_pay_payable_others
    pro_pay_payables.select { |ppp| ppp.other? }
  end
end
