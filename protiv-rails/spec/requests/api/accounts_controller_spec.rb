# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::AccountsController do
  let(:existing) { fixture(:user, :john_appleseed) }
  let(:name) { existing.name }
  let(:email) { existing.email }
  let(:password) { SpecHelpers::Fixtures::DEFAULT_PASSWORD }

  describe "#create" do
    let(:account_params) do
      {
        data: {
          type: "account",
          attributes: {
            name: name,
            email: email,
            password: password
          }
        }
      }
    end

    context 'new user' do
      # Destroy the existing John Appleseed to prevents email collision when
      # re-creating them in the tests.
      before { existing.destroy! }

      it "successful sign up", api_scenario: "sign-up/success" do
        api_fixture("create-account") do
          jsonapi_post api_accounts_path, account_params
        end

        # Front-end then follows up with a sign-in request
        api_fixture("create-session") do
          jsonapi_post api_session_path, {
            email: email,
            password: password
          }
        end

        user = User.last
        expect(user.organizations).to be_empty

        @jsonapi_data = nil
        api_fixture("get-account") do
          jsonapi_get api_account_path(user.id), headers: auth_for(user)
        end

        expect(jsonapi_data.relationships.dig("organization", "data", "id")).to be_nil
      end

      context "weak password" do
        let(:password) { "1234" }

        it "returns an error", api_scenario: "sign-up/weak-password" do
          api_fixture("create-account") do
            jsonapi_post api_accounts_path, account_params
          end
        end
      end

      context "with an invitation" do
        let(:organization) { fixture(:organization, :protiv) }
        let(:admin_user) { fixture(:user, :david) }

        let(:invitation) do
          Invitation.create(invited_by: admin_user, organization: organization, role_type: :manager)
        end

        let(:invitation_token) do
          invitation.generate_token_for(:account_create)
        end

        let(:api_fixture_overrides) do
          { "request.body.data.attributes.invitation_token" => "invitation-abcdefg12345678" }
        end

        before do
          account_params[:data][:attributes][:invitation_token] = invitation_token
        end

        it "adds the user to the org with the correct role upon successful sign up", api_scenario: "sign-up/with-invitation" do
          api_fixture("create-account") do
            jsonapi_post api_accounts_path, account_params
          end

          user = User.last
          expect(user.email).to eq(existing.email)
          expect(user.organizations).to include(organization)
          expect(user.roles.where(organization: organization).last.role_type).to eq("manager")
        end
      end
    end

    context "email collision" do
      it "returns an error", api_scenario: "sign-up/duplicate-email" do
        api_fixture("create-account") do
          jsonapi_post api_accounts_path, account_params
        end
      end
    end
  end

  describe "#show" do
    shared_examples "with a user and organization" do |user_label, org_label|
      let(:user) { fixture(:user, user_label) }
      let(:organization) { fixture(:organization, org_label) }
      let(:headers) { auth_for(user, organization) }

      it "returns the account, user and organization", api_scenario: "accounts/#{user_label.to_s.dasherize}-#{org_label.to_s.dasherize}" do
        api_fixture("show-account") do
          jsonapi_get api_account_path(user.id), headers: headers
        end
      end
    end

    shared_examples "with a user and no organization" do |user_label|
      let(:user) { fixture(:user, user_label) }
      let(:headers) { auth_for(user) }

      it "returns the account, user and no organization", api_scenario: "accounts/#{user_label.to_s.dasherize}" do
        api_fixture("show-account") do
          jsonapi_get api_account_path(user.id), headers: headers
        end
      end
    end

    context "authorized" do
      context "with a current organization" do
        include_examples "with a user and organization", :david, :protiv
      end

      context "with no current organization" do
        include_examples "with a user and no organization", :david
      end

      context "with no organizations (needs onboarding)" do
        include_examples "with a user and no organization", :john_appleseed
      end
    end

    context "wrong user" do
      let(:user) { fixture(:user, :david) }
      let(:headers) { auth_for(user) }

      it "won't show another user's account", api_scenario: "accounts/invalid-credentials" do
        api_fixture("show-account") do
          jsonapi_get api_account_path(identify_fixture(:user, :john_appleseed)), headers: headers
        end
      end
    end

    context "unauthorized" do
      let(:headers) { {} }

      specify "#show" do
        jsonapi_get api_account_path(identify_fixture(:user, :john_appleseed)), headers: headers
        expect(response).to be_unauthorized
      end
    end

    # Exhaustively API fixtures for all fixture'd users + org, just so the FE
    # tests have them available. Some of these duplicates the above but the
    # fixture naming matches, so it should just get verified a second time.
    context "David" do
      context "no current org" do
        include_examples "with a user and no organization", :david
      end

      context "Protiv" do
        include_examples "with a user and organization", :david, :protiv
      end

      context "Lawn Mow Inc." do
        include_examples "with a user and organization", :david, :lawn_mow_inc
      end
    end

    context "Laura" do
      context "no current org" do
        include_examples "with a user and no organization", :laura
      end

      context "Lawn Mow Inc." do
        include_examples "with a user and organization", :laura, :lawn_mow_inc
      end
    end

    context "Luigi" do
      context "no current org" do
        include_examples "with a user and no organization", :luigi
      end

      context "Lawn Mow Inc." do
        include_examples "with a user and organization", :luigi, :lawn_mow_inc
      end

      context "Mario Plumbing" do
        include_examples "with a user and organization", :luigi, :mario_plumbing
      end
    end

    context "Mario" do
      context "no current org" do
        include_examples "with a user and no organization", :mario
      end

      context "Mario Plumbing" do
        include_examples "with a user and organization", :mario, :mario_plumbing
      end
    end
  end
end
