# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::OrganizationUsersController, type: :request do
  let(:organization) { create(:organization) }
  let(:admin_user) { create(:user, organization: organization, role_type: "admin") }
  let(:employee_user) { create(:user, organization: organization, role_type: "employee") }
  let(:headers) { auth_for(admin_user, organization) }
  let(:organization_id) { organization.id }

  describe "#index" do
    before do
      create_list(:user, 3, organization: organization, role_type: "employee")
    end

    it "returns all users in the organization" do
      jsonapi_get api_organization_users_path(organization_id: organization_id), headers: headers

      expect(response).to be_successful
      data = response.parsed_body

      expect(data.fetch("data").length).to eq(4)
    end

    context "when not authorized" do
      let(:headers) { {} }

      it "returns unauthorized" do
        jsonapi_get api_organization_users_path(organization_id: organization_id), headers: headers

        expect(response).to be_unauthorized
      end
    end
  end

  describe "#show" do
    let(:role) { employee_user.roles.find_by(organization: organization) }
    subject(:show_request) { jsonapi_get api_organization_user_path(organization_id: organization_id, id: role.id), headers: headers }
    subject(:response_data) { response.parsed_body }

    before do
      show_request
    end

    it "returns a successful response" do
      expect(response).to be_successful
    end

    it "returns the correct user id" do
      expect(response_data.dig("data", "id")).to eq(role.id.to_s)
    end

    it "returns the correct user name" do
      expect(response_data.dig("data", "attributes", "user_name")).to eq(employee_user.name)
    end

    it "returns the correct user email" do
      expect(response_data.dig("data", "attributes", "user_email")).to eq(employee_user.email)
    end
  end

  describe "#update" do
    let(:role) { employee_user.roles.find_by(organization: organization) }
    let(:params) do
      {
        data: {
          id: role.id.to_s,
          type: "organization-users",
          attributes: {
            user_name: "Updated Name",
            user_email: "<EMAIL>",
            user_phone: "************",
            user_phone_country_code: "1"
          }
        }
      }
    end

    subject(:update_request) do
      path = api_organization_user_path(organization_id: organization_id, id: role.id)
      jsonapi_patch path, params, headers: headers
    end

    before do
      update_request
      employee_user.reload
    end

    it "returns a successful response" do
      expect(response).to be_successful
    end

    it "updates the user name" do
      expect(employee_user.name).to eq("Updated Name")
    end

    it "updates the user email" do
      expect(employee_user.email).to eq("<EMAIL>")
    end

    it "updates the user phone" do
      expect(employee_user.phone).to eq("************")
    end

    it "updates the user phone country code" do
      expect(employee_user.phone_country_code).to eq("1")
    end
  end

  describe "status actions" do
    let(:role) { employee_user.roles.find_by(organization: organization) }

    describe "#deactivate" do
      subject(:deactivate_request) do
        path = deactivate_api_organization_user_path(organization_id: organization_id, id: role.id)
        jsonapi_post path, {}, headers: headers
      end

      before do
        deactivate_request
        role.reload
      end

      it "returns a successful response" do
        expect(response).to be_successful
      end

      it "deactivates the role" do
        expect(role.active).to be false
      end

      it "sets the status to inactive" do
        expect(role.status).to eq("inactive")
      end
    end

    describe "#activate" do
      before do
        role.update(active: false, status: "inactive")
      end

      subject(:activate_request) do
        path = activate_api_organization_user_path(organization_id: organization_id, id: role.id)
        jsonapi_post path, {}, headers: headers
      end

      before do
        activate_request
        role.reload
      end

      it "returns a successful response" do
        expect(response).to be_successful
      end

      it "activates the role" do
        expect(role.active).to be true
      end

      it "sets the status to active" do
        expect(role.status).to eq("active")
      end
    end
  end
end
