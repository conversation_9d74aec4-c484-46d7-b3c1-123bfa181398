# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::UsersController do
  let(:params) { {} }
  let(:headers) { {} }

  def make_get_request(request_path = path)
    jsonapi_get request_path, params: params, headers: headers
  end

  def make_patch_request(request_path = path)
    jsonapi_patch request_path, params, headers: headers
  end

  def make_password_patch_request(request_path = path)
    request_headers = { 'Content-Type' => 'application/vnd.api+json' }
    # Convert headers to hash if it's an auth object
    request_headers.merge!(headers.to_hash) if headers.respond_to?(:to_hash)

    patch request_path, params: params.to_json, headers: request_headers
  end

  describe "#index" do
    let(:path) { api_users_path }

    context "unauthorized" do
      specify do
        make_get_request

        expect(response).to be_unauthorized
      end
    end

    context "authorized" do
      let(:user) { fixture(:user, :david) }
      let(:headers) { auth_for(user) }

      specify do
        make_get_request
        expect(response).to be_successful
        data = response.parsed_body.fetch("data")
        expect(data.length).to eq(1)

        expect(data.first["type"]).to eq("user")
        expect(data.first["id"].to_s).to eq(user.id.to_s)
      end
    end
  end

  describe "#show" do
    let(:user) { fixture(:user, :david) }
    let(:path) { api_user_path(user) }

    context "unauthorized" do
      specify do
        make_get_request

        expect(response).to be_unauthorized
      end
    end

    context "authorized" do
      let(:headers) { auth_for(user) }
      let(:other_user) { fixture(:user, :john_appleseed) }

      specify do
        make_get_request
        expect(response).to be_successful
        data = response.parsed_body.fetch("data")

        expect(data["type"]).to eq("user")
        expect(data["id"].to_s).to eq(user.id.to_s)
      end

      specify do
        make_get_request(api_user_path(other_user))

        expect(response).to be_not_found
      end
    end
  end

  describe "#update" do
    let(:user) { fixture(:user, :david) }
    let(:path) { api_user_path(user) }

    let(:valid_params) do
      {
        data: {
          type: "user",
          id: user.id.to_s,
          attributes: {
            name: "Updated Name",
            email: "<EMAIL>"
          }
        }
      }
    end

    let(:invalid_params) do
      {
        data: {
          type: "user",
          id: user.id.to_s,
          attributes: {
            name: "" # Invalid - name cannot be blank
          }
        }
      }
    end

    context "unauthenticated" do
      specify do
        make_patch_request
        expect(response).to be_unauthorized
      end
    end

    context "unauthorized" do
      let(:other_user) { fixture(:user, :john_appleseed) }
      let(:path) { api_user_path(other_user) }
      let(:headers) { auth_for(user) }
      let(:params) { valid_params }

      it "returns unauthorized status" do
        make_patch_request
        expect(response).to be_unauthorized
      end
    end

    context "authenticated and authorized" do
      let(:headers) { auth_for(user) }

      context "with valid parameters" do
        let(:params) { valid_params }

        it "successfully updates the user" do
          make_patch_request
          expect(response).to be_successful

          user.reload
          expect(user.name).to eq("Updated Name")
          expect(user.email).to eq("<EMAIL>")
          expect(user.verified).to be(false) # Email was changed
        end
      end

      context "with invalid parameters" do
        let(:params) { invalid_params }

        it "returns validation errors" do
          make_patch_request
          expect(response).to have_http_status(:unprocessable_entity)

          errors = response.parsed_body["errors"]
          expect(errors).to be_present
        end
      end
    end
  end

  describe "#change_password" do
    let(:default_password) { SpecHelpers::Fixtures::DEFAULT_PASSWORD }
    let(:user) { fixture(:user, :david, password: default_password) }
    let(:path) { password_api_user_path(user) }

    before do
      # Ensure the password is set correctly for the test
      user.update!(password: default_password, password_confirmation: default_password)
    end

    let(:valid_password_params) do
      {
        data: {
          type: "users",
          id: user.id.to_s,
          attributes: {
            current_password: default_password,
            new_password: "new-secure-password123"
          }
        }
      }
    end

    let(:invalid_current_password_params) do
      {
        data: {
          type: "users",
          id: user.id.to_s,
          attributes: {
            current_password: "wrong-password",
            new_password: "new-secure-password123"
          }
        }
      }
    end

    let(:invalid_new_password_params) do
      {
        data: {
          type: "users",
          id: user.id.to_s,
          attributes: {
            current_password: default_password,
            new_password: "short"  # Too short
          }
        }
      }
    end

    context "unauthenticated" do
      let(:params) { valid_password_params }

      specify do
        make_password_patch_request
        expect(response).to be_unauthorized
      end
    end

    context "unauthorized" do
      let(:other_user) { fixture(:user, :john_appleseed) }
      let(:path) { password_api_user_path(other_user) }
      let(:headers) { auth_for(user) }
      let(:params) { valid_password_params }

      specify do
        make_password_patch_request
        expect(response).to be_unauthorized
      end
    end

    context "authenticated and authorized" do
      let(:headers) { auth_for(user) }

      context "with valid parameters" do
        let(:params) { valid_password_params }

        it "changes the password and sets the pending verification flag" do
          expect {
            make_password_patch_request
          }.to have_enqueued_mail(UserMailer, :password_change_verification_email)

          expect(response).to have_http_status(:ok)
          expect(response.parsed_body).to include("message" => "Password change initiated. Please check your email to verify.")

          user.reload
          expect(user.password_change_pending_verification).to be(true)
          # Verify password is changed by authenticating with the new password
          expect(user.authenticate("new-secure-password123")).to eq(user)
        end
      end

      context "with incorrect current password" do
        let(:params) { invalid_current_password_params }

        it "returns an error" do
          expect {
            make_password_patch_request
          }.not_to have_enqueued_mail(UserMailer, :password_change_verification_email)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response.parsed_body["errors"]).to include(hash_including("code" => "invalid_current_password"))
        end
      end

      context "with invalid new password" do
        let(:params) { invalid_new_password_params }

        it "returns validation errors" do
          expect {
            make_password_patch_request
          }.not_to have_enqueued_mail(UserMailer, :password_change_verification_email)

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response.parsed_body["errors"]).to include(hash_including("code" => "invalid_password"))
        end
      end
    end
  end

  describe "#verify_password_change" do
    let(:user) { fixture(:user, :david, password_change_pending_verification: true) }
    let(:token) { user.generate_token_for(:password_change_verification) }
    let(:path) { verify_password_change_api_user_path(user) }
    let(:params) { { token: token } }

    def make_verification_request(request_path = path)
      get request_path, params: params
    end

    context "with a valid token" do
      it "verifies the password change" do
        make_verification_request

        expect(response).to have_http_status(:ok)
        expect(response.parsed_body).to include("message" => "Password change verified successfully.")

        user.reload
        expect(user.password_change_pending_verification).to be(false)
      end
    end

    context "with an invalid token" do
      let(:params) { { token: "invalid-token" } }

      it "returns an error" do
        make_verification_request

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body["errors"]).to include(hash_including("detail" => "Invalid or expired token"))

        user.reload
        expect(user.password_change_pending_verification).to be(true)
      end
    end

    context "when token belongs to a different user" do
      let(:other_user) { fixture(:user, :john_appleseed, password_change_pending_verification: true) }
      let(:token) { other_user.generate_token_for(:password_change_verification) }

      it "returns an error" do
        make_verification_request

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body["errors"]).to include(hash_including("detail" => "Token does not match this user"))

        user.reload
        expect(user.password_change_pending_verification).to be(true)
      end
    end

    context "when user ID does not exist" do
      it "returns not found" do
        get verify_password_change_api_user_path(id: "non-existent"), params: { token: token }

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
