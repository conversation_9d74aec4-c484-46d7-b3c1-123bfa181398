# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::MilestonesController do
  context "not authorized" do
    specify do
      jsonapi_get(api_milestones_path)

      expect(controller.current_user).to be_nil
      expect(controller.current_organization).to be_nil
      expect(response).to be_unauthorized
    end
  end

  context "authorized" do
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:user) { fixture(:user, :luigi) }
    let(:identity) { fixture(:identity, :luigi) }
    let(:employee) { fixture(:identity, :louis) }
    let(:job) { fixture(:job, :lawn_q2) }
    let(:milestone) { fixture(:milestone, :lawn_q2_milestone_1) }
    let(:headers) { auth_for(user, organization) }

    describe "GET show" do
      it "returns the requested milestone", api_scenario: "milestone" do
        jsonapi_get(
          api_milestone_path(milestone),
          headers: headers
        )

        expect(response).to be_successful

        data = jsonapi_data
        expect(data.jsonapi_type).to eq("milestone")
        expect(data.id).to eq(milestone.id)
        expect(data.attributes[:status]).to eq("in_progress")
      end

      it "includes employee_count attribute" do
        jsonapi_get(
          api_milestone_path(milestone),
          headers: headers
        )

        expect(response).to be_successful
        expect(jsonapi_data.attributes[:employee_count]).to be_present
      end

      it "includes related job and milestone_times when requested" do
        jsonapi_get(
          api_milestone_path(milestone, include: "job,milestone_times"),
          headers: headers
        )

        expect(response).to be_successful

        data = jsonapi_data
        expect(data.sideload(:job).id).to eq(job.id)
        expect(data.sideload(:milestone_times)).not_to be_empty
      end

      it "includes related employee_milestone_times when requested and paginated", api_scenario: "milestones" do
        api_fixture("get-with-employee-milestone-times") do
          jsonapi_get(
            api_milestone_path(
              milestone,
              include: "employee_milestone_times.identity,job",
              page: {
                "employee_milestone_times.number": 0,
                "employee_milestone_times.size": 5
              }
            ),
            headers: headers
          )
        end

        expect(response).to be_successful
        employee_milestone_times = jsonapi_data.sideload(:employee_milestone_times)
        expect(employee_milestone_times.size).to eq(1)
        expect(employee_milestone_times.first.sideload(:identity).id).to eq(employee.id)
        # TODO: add more milestone_times to the fixture and test that the pagination works
      end

      describe "with progress tracking" do
        let(:organization) { milestone.organization }
        let(:milestone_item) { create(:milestone_item, milestone:, organization:, catalog_item:) }
        let(:catalog_item) { create(:catalog_item, organization:) }

        before { milestone.update!(tracked_milestone_item: milestone_item) }

        it "returns the requested milestone", api_scenario: "milestone-material-tracking" do
          api_fixture("get-with-progress-tracking-material") do
            jsonapi_get(
              api_milestone_path(milestone, include: "tracking_material"),
              headers: headers,
            )
          end

          expect(response).to be_successful

          data = jsonapi_data
          tracking_material = data.sideloads(:tracking_material)
          expect(data.jsonapi_type).to eq("milestone")
          expect(data.id).to eq(milestone.id)
          expect(data.attributes[:status]).to eq("in_progress")

          expect(tracking_material.material_name).to eq("unknown material")
          expect(tracking_material.budget_quantity).to eq(100.0)
          expect(tracking_material.install_rate_per_hour.round(1)).to eq(1.7)
          expect(tracking_material.install_rate).to eq({ "cents" => 2864, "currency_iso" => "USD" })
          expect(tracking_material.actual_quantity).to eq(0.0)
          expect(tracking_material.percent_complete).to eq(0.0)
        end

        context "with item allocations" do
          before do
            milestone.item_allocations.create!(
              catalog_item:,
              item_quantity: milestone_item.quantity / 2.0,
              organization:,
              unit_cost_cents: 200,
              total_cost_cents: milestone_item.quantity * 200 / 2.0
            )
            Sidekiq::Worker.drain_all
          end

          it "returns the requested milestone", api_scenario: "milestone-material-tracking-allocations" do
            api_fixture("get-with-progress-tracking-material-allocations") do
              jsonapi_get(
                api_milestone_path(milestone, include: "tracking_material"),
                headers: headers,
              )
            end

            expect(response).to be_successful

            data = jsonapi_data
            tracking_material = data.sideloads(:tracking_material)
            expect(data.jsonapi_type).to eq("milestone")
            expect(data.id).to eq(milestone.id)
            expect(data.attributes[:status]).to eq("in_progress")

            expect(tracking_material.material_name).to eq("unknown material")
            expect(tracking_material.budget_quantity).to eq(100.0)
            expect(tracking_material.install_rate_per_hour.round(1)).to eq(1.7)
            expect(tracking_material.install_rate).to eq({ "cents" => 2864, "currency_iso" => "USD" })
            expect(tracking_material.actual_quantity).to eq(50.0)
            expect(tracking_material.percent_complete).to eq(50.0)
          end
        end
      end
    end
  end
end
