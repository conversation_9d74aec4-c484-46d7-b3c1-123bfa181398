# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::JobDetailsController do
  context "unauthenticated" do
    specify do
      jsonapi_get(api_job_detail_path(identify_fixture(:job, :lawn_q1)))

      expect(controller.current_user).to eq(nil)
      expect(controller.send(:current_organization)).to eq(nil)
      expect(response).to be_unauthorized
    end
  end

  let(:details_include) { "milestones.resolved_pro_pay,milestones.milestone_times.identity,property,branch" }

  context "authorized" do
    let(:user) { fixture(:user, :luigi) }
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:headers) { auth_for(user, organization) }
    let(:job) { fixture(:job, :lawn_q2) }

    before do
      # NOTE: this isn't ideal, but we don't want to include a sort param in the url
      # (unless we are actually going to use it). It's otherwise quite difficult to get these
      # into a stable sort
      allow(MilestoneTimeResource).to receive(:default_sort) do
        [{ start_time: :asc }]
      end
    end

    it "returns job details with milestones, resolved_pro_pay, milestone_times, and identity relationships", api_scenario: "job-details" do
      api_fixture("get-with-milestones") do
        jsonapi_get(api_job_detail_path(job, include: details_include), headers: headers)
      end

      expect(response).to be_successful

      data = jsonapi_data
      # Verify the job data
      expect(data.id).to eq(job.id)

      # Verify milestones relationships
      milestones = data.sideload(:milestones)
      expect(milestones.size).to eq(3)
      expect(milestones.map(&:id)).to contain_exactly(1, 2, 3)
      expect(milestones.map(&:description)).to contain_exactly("Milestone 1", "Milestone 2", "Milestone 3")

      milestones.each_with_index do |milestone, index|
        # TODO: Test SubProPays if we need them
        # sub_pro_pay = milestone.sideload(:resolved_sub_pro_pay)
        # expect(sub_pro_pay).not_to be_nil
        #
        # if index < 2
        #   expect(sub_pro_pay.id).to eq(1)
        # else
        #   expect(sub_pro_pay.id).to eq(2)
        # end

        pro_pay = milestone.sideload(:resolved_pro_pay)

        if index < 2
          expect(pro_pay).not_to be_nil
          expect(pro_pay.id).to eq(1)
        else
          expect(pro_pay).to be_nil
        end

        milestone_times = milestone.sideload(:milestone_times)
        milestone_times.each do |milestone_time|
          identity = milestone_time.sideload(:identity)
          expect(identity).not_to be_nil
        end
      end
    end
  end
end
