# frozen_string_literal: true

require "rails_helper"

RSpec.describe Api::EmployeeMilestoneTimesController do
  context "when user is not authenticated" do
    specify do
      jsonapi_get(api_employee_milestone_times_path)

      expect(controller.current_user).to be_nil
      expect(controller.current_organization).to be_nil
      expect(response).to be_unauthorized
    end
  end

  describe "authorized" do
    let(:organization) { fixture(:organization, :lawn_mow_inc) }
    let(:user) { fixture(:user, :luigi) }
    let(:identity) { fixture(:identity, :luigi) }
    let(:employee) { fixture(:identity, :louis) }
    let(:job) { fixture(:job, :lawn_q2) }
    let(:milestone) { fixture(:milestone, :lawn_q2_milestone_1) }

    let(:headers) { auth_for(user, organization) }

    it "filters by milestone_id", api_scenario: "employee-milestone-times" do
      api_fixture("get-by-milestone") do
        jsonapi_get(
          api_employee_milestone_times_path(
            filter: { milestone_id: milestone.id },
            include: "identity,milestone"
          ),
          headers: headers
        )
      end

      expect(response).to be_successful

      data = jsonapi_data

      expect(data.size).to eq(1)
      expect(data[0].jsonapi_type).to eq("employee-milestone-time")
      expect(data.first.sideload(:milestone).id).to eq(milestone.id)
    end

    it "filters by milestone_id and identity_id" do
      jsonapi_get(
        api_employee_milestone_times_path(
          filter: { milestone_id: milestone.id, identity_id: employee.id },
          include: "milestone.job,identity",
          fields: {
            job: "name"
          }
        ),
        headers: headers
      )

      expect(response).to be_successful

      data = jsonapi_data

      expect(data.size).to eq(1)
      expect(data[0].jsonapi_type).to eq("employee-milestone-time")
      expect(data.first.sideload(:milestone).id).to eq(milestone.id)
      expect(data.first.sideload(:identity).id).to eq(employee.id)
    end
  end
end
