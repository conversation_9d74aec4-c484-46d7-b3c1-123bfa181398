# frozen_string_literal: true

module SpecHelpers
  module Sync
    module Test
      class Adapter < ::Sync::Abstract::Adapter
        def retrieve_credential
        end

        def refresh_credential
        end

        def remote_helper_models
          []
        end

        def retrieve_identities(**options)
          (_stubs[:identities] || []).map do |json|
            remote_record_class.new(
              json, remote_resource: "test_identity"
            )
          end
        end

        def stub_identity(json)
          _stubs[:identities] ||= []
          _stubs[:identities] << json.merge("id" => _stubs[:identities].count + 1)
        end

        def _stubs
          @_stubs ||= {}
        end
      end

      class Materializer < ::Sync::Abstract::Materializer
      end

      class Record < ::Sync::RemoteRecord
        def primary_id
          model.fetch('id')
        end
      end
    end

    module TestA
      extend ::Sync::CommonClassMethods

      RESOURCE_MAP = {
        test_identity: :identity
      }

      def self.source
        "test_a"
      end

      def self.adapter
        Adapter
      end

      def self.materializer
        Materializer
      end

      def self.capabilities
        {
          clock_times: [:create, :pull],
          identities: [:pull],
          jobs: [],
          branches: [],
          milestones: [],
          milestone_times: [],
          routes: [],
          milestone_items: [],
          item_allocations: [],
          services: [],
          catalog_items: []
        }
      end

      def self.cache_to_resource(cache)
        cache
      end

      class Adapter < Test::Adapter
        def remote_record_class
          Record
        end

        def create_identities(...)
          fixme!
        end

        def create_clock_time(attendance)
          raise "stub this method if you expect it to be called during a test"
        end

        def retrieve_clock_times(**opts)
          raise "stub this method if you expect it to be called during a test"
        end
      end

      class Materializer < Test::Materializer
        def source
          TestA.source
        end

        def materialize_identity(identity_cache)
          remote_id = identity_cache.remote_primary_id
          remote_resource = identity_cache.remote_resource

          remote_slug = remote_slug(remote_id, remote_resource)

          correlation_id = identity_correlation_id(identity_cache)
          identity = integration_records.where(remote_slug:).first&.record
          identity ||= Identity.new(remote_slug: remote_slug(remote_id, remote_resource), organization_id: organization.id)
          identity.name = identity_cache.raw_data.fetch("name")
          identity.employee_id = identity_cache.raw_data['employee_id']
          identity.save
        end

        def identity_correlation_id(identity_cache)
          fixme! # FIXME: need a strategy here - Correlator object?
        end
      end

      class Record < SpecHelpers::Sync::Test::Record
        def source
          TestA.source
        end
      end

      ::Sync.register_module(self)
    end

    module TestB
      extend ::Sync::CommonClassMethods

      def self.source
        "test_b"
      end

      def self.adapter
        Adapter
      end

      def self.materializer
        Materializer
      end

      def self.cache_to_resource(cache)
        cache
      end

      def self.capabilities
        {
          clock_times: [],
          identities: [:create, :update],
          jobs: [],
          branches: [],
          milestones: [],
          milestone_times: [],
          routes: [],
          milestone_items: [],
          item_allocations: [],
          services: [],
          catalog_items: []
        }
      end

      class Adapter < Test::Adapter
        def create_identity(...)
          fixme!
        end

        def update_identity(...)
          fixme!
        end
      end

      class Materializer < Test::Materializer
      end

      ::Sync.register_module(self)
    end
  end

  module FakeRemoteA
  end

  module FakeRemoteB
  end
end
