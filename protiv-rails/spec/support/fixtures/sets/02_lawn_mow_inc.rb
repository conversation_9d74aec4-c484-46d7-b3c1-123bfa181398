# frozen_string_literal: true

module SpecHelpers
  module Fixtures
    # Story:
    #
    # Lawn Mow Inc. is <PERSON>'s landscaping business servicing the greater NY
    # area.
    #
    # As a convention, all the fictional workers and places starts with "L"
    # to make them easy to identify (great task for ChatGPT) and to avoid
    # accidental conflict with other fixture sets.
    #
    # The intention is to have the minimum amount of fixtures in here to make
    # it representative of a real but small customer.
    class LawnMowInc < FixtureSet
      def load!
        setup_org
        setup_users
        setup_branches
        setup_routes
        setup_properties
        setup_jobs
        setup_milestones
        setup_milestone_times
        setup_pro_pays
      end

      def setup_org
        @org = create_fixture(
          :organization,
          :lawn_mow_inc,
          name: 'Lawn Mow Inc.',
        )

        create_fixture(
          :integration,
          :lawn_mow_inc,
          organization: @org,
        )
      end

      def setup_users
        AddUserToOrganization.new(
          user: fixture(:user, :david),
          organization: @org,
          role_type: "admin"
        ).execute!

        # <PERSON> is the manager that oversees the operations. She only works at
        # Lawn Mow Inc. and does not have access to any other organizations.
        @laura = create_fixture(
          :user,
          :laura,
          email: '<EMAIL>',
          password: DEFAULT_PASSWORD,
          verified: true,
          organization: @org,
          role_type: 'manager',
        )
        @laura_identity = create_fixture(
          :identity,
          :laura,
          user: @laura,
          organization: @org,
          name: '<PERSON>',
          crew_lead: true
        )

        # <PERSON> is the assistant manager. He helps out here, but naturally, his
        # main job is at <PERSON> Plumbing. In general, as a matter of default, it
        # is a good idea to use <PERSON> as the test user for accessing resources
        # from Lawn Mow Inc. – since he has access to more than one org, it
        # implicitly ensures all accessed resources are scoped properly and
        # there is no bleed-over between orgs.
        @luigi = create_fixture(
          :user,
          :luigi,
          email: '<EMAIL>',
          password: DEFAULT_PASSWORD,
          verified: true,
          organization: @org,
          role_type: 'manager',
        )
        @luigi_identity = create_fixture(
          :identity,
          :luigi,
          user: @luigi,
          organization: @org,
          name: 'Luigi',
          crew_lead: true
        )

        # Louis is a worker at Lawn Mow Inc.
        @louis = create_fixture(
          :user,
          :louis,
          email: '<EMAIL>',
          password: DEFAULT_PASSWORD,
          verified: true,
          organization: @org,
          role_type: 'employee',
        )
        @louis_identity = create_fixture(
          :identity,
          :louis,
          user: @louis,
          organization: @org,
          name: 'Louis',
        )
      end

      def setup_branches
        @lakewood = create_fixture(
          :branch,
          :lakewood,
          name: 'Lakewood',
          organization: @org,
        )

        @louisville = create_fixture(
          :branch,
          :louisville,
          name: 'Louisville',
          organization: @org,
        )
      end

      def setup_properties
        # Lakeside Estates - a luxury residential complex
        @lakeside_estates = create_fixture(
          :property,
          :lakeside_estates,
          organization: @org,
          name: 'Lakeside Estates',
          address_line_1: '123 Lakeside Drive',
          address_line_2: 'Suite 100',
          address_city: 'Lakewood',
          address_state_province: 'NY',
          address_zip_code: '10001',
          primary_contact: @laura_identity
        )

        # Linden Park - a public park
        @linden_park = create_fixture(
          :property,
          :linden_park,
          organization: @org,
          name: 'Linden Park',
          address_line_1: '456 Linden Avenue',
          address_city: 'Louisville',
          address_state_province: 'NY',
          address_zip_code: '10002',
          primary_contact: @luigi_identity
        )

        # Lakeview Shopping Center - a commercial property
        @lakeview_shopping = create_fixture(
          :property,
          :lakeview_shopping,
          organization: @org,
          name: 'Lakeview Shopping Center',
          address_line_1: '789 Lakeview Blvd',
          address_city: 'Lakewood',
          address_state_province: 'NY',
          address_zip_code: '10001',
          geolocation_latitude: 40.7128,
          geolocation_longitude: -74.0060,
          primary_contact: @laura_identity
        )
      end

      def setup_routes
        @lakewood_route_1 = create_fixture(
          :route,
          :lakewood_route_1,
          name: 'Lakewood Route 1',
          create_grouped_pro_pays: false,
          branch: @lakewood,
          organization: @org
        )

        @lakewood_route_2 = create_fixture(
          :route,
          :lakewood_route_2,
          name: 'Lakewood Route 2',
          create_grouped_pro_pays: false,
          branch: @lakewood,
          organization: @org
        )

        @louisville_route_1 = create_fixture(
          :route,
          :louisville_route_1,
          name: 'Louisville Route 1',
          create_grouped_pro_pays: false,
          branch: @louisville,
          organization: @org
        )
      end

      def setup_jobs
        create_fixture(
          :job,
          :lawn_snow_removal,
          name: '2024/2025 Snow Removal',
          branch: @louisville,
          organization: @org,
          property: @linden_park,
          status: :in_progress,
          manager: @laura_identity,
          last_activity_at: 45.days.ago,
          created_date: 60.days.ago
        )

        create_fixture(
          :job,
          :lawn_q1,
          name: 'Q1 Maintenance Service',
          organization: @org,
          branch: @lakewood,
          property: @lakeside_estates,
          status: :completed,
          manager: @luigi_identity,
          last_activity_at: 10.days.ago,
          created_date: 20.days.ago
        )

        @job_lawn_q2 = create_fixture(
          :job,
          :lawn_q2,

          factory: :job_with_milestones,
          # Manually setting up milestones for this job
          milestone_count: 0,
          remote_reference: "ABCDE",

          name: 'Q2 Maintenance Service',
          organization: @org,
          branch: @lakewood,
          property: @lakeside_estates,
          job_type: "recurring",
          status: :in_progress,
          manager: @luigi_identity,
          last_activity_at: 3.days.ago,
          created_date: 30.days.ago,
        )

        create_fixture(
          :job,
          :lawn_q3,
          name: 'Q3 Maintenance Service',
          organization: @org,
          branch: @lakewood,
          property: @lakeview_shopping,
          status: :pending,
          # Not yet assigned
          manager: nil,
          last_activity_at: 4.days.ago,
          created_date: 40.days.ago
        )

        create_fixture(
          :job,
          :lawn_emergency,
          name: 'Emergency Sweeping',
          branch: @louisville,
          organization: @org,
          property: @linden_park,
          status: :completed,
          manager: @laura_identity,
          last_activity_at: 2.days.ago,
          created_date: 20.days.ago
        )
      end

      def setup_milestones
        # TODO: Add realistic names/descriptions for these milestones once the
        # model's description is properly implemented

        # For job lawn_q2
        @lawn_q2_milestone_1 = create_fixture(
          :milestone,
          :lawn_q2_milestone_1,
          job: @job_lawn_q2,
          status: :in_progress,
          name: "Light Maintenance",
          seconds_budget: 614592,
          seconds_cost: 332640,
          contract_price: Money.from_amount(26969.11),
          material_budget: Money.from_amount(8883.57),
          material_cost: Money.from_amount(5475.37),
          labor_budget: Money.from_amount(2864.40),
          labor_cost: Money.from_amount(4368.73),
          percent_complete_hundredths: 10
        )
        @lawn_q2_milestone_2 = create_fixture(
          :milestone,
          :lawn_q2_milestone_2,
          job: @job_lawn_q2,
          status: :in_progress,
          name: "Light Installation",
          seconds_budget: 95400,
          seconds_cost: 314676,
          contract_price: Money.from_amount(7438.54),
          material_budget: Money.from_amount(2533.57),
          material_cost: Money.from_amount(2712.63),
          labor_budget: Money.from_amount(821.50),
          labor_cost: Money.from_amount(2279.99),
          equipment_budget: Money.from_amount(0),
          equipment_cost: Money.from_amount(0),
          percent_complete_hundredths: 10
        )
        @lawn_q2_milestone_3 = create_fixture(
          :milestone,
          :lawn_q2_milestone_3,
          job: @job_lawn_q2,
          status: :pending,
          name: "Landscape Installation",
          seconds_budget: 75600,
          seconds_cost: 99036,
          contract_price: Money.from_amount(4970.34),
          material_budget: Money.from_amount(1019.46),
          material_cost: Money.from_amount(200.51),
          labor_budget: Money.from_amount(647.00),
          labor_cost: Money.from_amount(688.04),
          equipment_budget: Money.from_amount(0),
          equipment_cost: Money.from_amount(0),
          percent_complete_hundredths: 10
        )
      end

      def setup_milestone_times
        create_fixture(
          :milestone_time,
          :lawn_q2_milestone_1_milestone_time_1,
          milestone: @job_lawn_q2.milestones.first,
          identity: @louis_identity,
          identity_id: @louis_identity.id,
          base_hourly_rate: Money.from_amount(20),
          start_time: 8.days.ago,
          end_time: 8.days.ago + 9.hours,
        )
        create_fixture(
          :milestone_time,
          :lawn_q2_milestone_1_milestone_time_2,
          milestone: @job_lawn_q2.milestones.first,
          identity: @louis_identity,
          identity_id: @louis_identity.id,
          base_hourly_rate: Money.from_amount(25),
          start_time: 7.days.ago,
          end_time: 7.days.ago + 8.hours,
        )
      end

      def setup_pro_pays
        @pro_pay = create_fixture(
          :pro_pay,
          :pro_pay,
          organization: @org
        )
        @sub_pro_pay_1 = create_fixture(
          :sub_pro_pay,
          :sub_pro_pay_1,
          pro_pay: @pro_pay
        )
        @sub_pro_pay_2 = create_fixture(
          :sub_pro_pay,
          :sub_pro_pay_2,
          pro_pay: @pro_pay
        )
        create_fixture(
          :sub_pro_pay_pro_payable,
          :sub_pro_pay_pro_payable_1,
          sub_pro_pay: @sub_pro_pay_1,
          pro_payable: @lawn_q2_milestone_1
        )
        create_fixture(
          :sub_pro_pay_pro_payable,
          :sub_pro_pay_pro_payable_2,
          sub_pro_pay: @sub_pro_pay_1,
          pro_payable: @lawn_q2_milestone_2
        )
        # Note: @lawn_q2_milestone_2 should not be associated with a ProPay
      end
    end
  end
end
