# frozen_string_literal: true

FactoryBot.define do
  sequence(:organization_name) { |n| "Org #{n}" }
  factory :organization do
    name { generate(:organization_name) }

    factory :organization_with_crew do
      with_crew
    end

    factory :organization_with_jobs_milestones do
      with_crew
      with_job_with_milestones
      with_milestone_times
    end

    trait :with_crew do
      name { "Org with crew" }

      transient do
        identity_count { 5 }
        branch_count { 2 }
        route_count { 2 }
      end

      identities do
        Array.new(identity_count) do |idx|
          # Create the first two as crew leads
          create(:identity, crew_lead: idx < 2)
        end
      end

      branches do
        Array.new(branch_count) do |_idx|
          create(:branch)
        end
      end

      routes do
        Array.new(route_count) do |_idx|
          create(:route)
        end
      end
    end

    trait :with_payroll_schedules do
      transient do
        payroll_schedule_count { 2 }
      end

      payroll_schedules do
        Array.new(payroll_schedule_count) do |_idx|
          create(:payroll_schedule_with_period)
        end
      end
    end

    trait :with_payroll_schedules_no_pay_periods do
      transient do
        payroll_schedule_count { 2 }
      end

      payroll_schedules do
        Array.new(payroll_schedule_count) do |_idx|
          create(:payroll_schedule)
        end
      end
    end

    trait :with_job_with_milestones do
      transient do
        job_count { 1 }
      end

      jobs do
        Array.new(job_count) do |_idx|
          create(:job_with_milestones)
        end
      end
    end
  end
end
