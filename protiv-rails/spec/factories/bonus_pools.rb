# frozen_string_literal: true

FactoryBot.define do
  sequence(:bonus_pool_name) { |n| "pool-#{n}" }

  factory :bonus_pool do
    name { generate(:bonus_pool_name) }
    association :organization

    trait(:division_1) do
      crew_percent_hundredths { 50_00 }
      company_percent_hundredths { 25_00 }
      manager_percent_hundredths { 25_00 }
      crew_retention_percent_hundredths { 50_00 }
    end

    trait(:division_2) do
      crew_lead_percent_hundredths { 5_00 }
      company_percent_hundredths { 15_00 }
      manager_percent_hundredths { 8_00 }
      other_percent_hundredths { 12_00 }
      crew_percent_hundredths { 60_00 }
      crew_retention_percent_hundredths { 27_00 }
    end

    factory :bonus_pool_with_others do
      after(:create) do |bonus_pool, _evaluator|
        create(:bonus_pool_payable_with_other, bonus_pool: bonus_pool)
        bonus_pool.reload
      end
    end

    factory :bonus_pool_with_two_others do
      after(:create) do |bonus_pool, _evaluator|
        create_list(:bonus_pool_payable_with_other, 2, bonus_pool: bonus_pool)
        bonus_pool.reload
      end
    end
  end
end
