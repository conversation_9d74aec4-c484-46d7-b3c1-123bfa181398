# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Invitation, type: :model do
  let(:organization) { create(:organization) }

  specify do
    invitation = Invitation.create(role_type: :admin, organization: organization)
    expect(invitation).to be_valid
    token = invitation.generate_token_for(:account_create)

    new_invitation = Invitation.find_by_token_for(:account_create, token)

    expect(new_invitation).to eq(invitation)
  end

  pending "questions" do
    # - there are potentially several invitation types:
    #   - 'admin' / 'manager' would be granted permissions to act on behalf of the company
    #     so should have additional verification — require that the email address
    #     matches?
    #   - 'employee' could include an employee id
    # - should we include "invited by?"
    # - should this just be active-record backed?
    expect(true).to eq(false)
  end

  describe "#claim!" do
    let(:invitation) do
      Invitation.create(
        organization: organization,
        role_type: "admin"
      )
    end
    let(:user) { create(:user) }
    let(:organization) { create(:organization) }

    context "when user has an existing org membership" do
      before do
        AddUserToOrganization.new(
          user: user,
          organization: organization,
          role_type: "employee"
        ).execute!
      end

      it "keeps the old and new roles" do
        invitation.claim!(user)

        invitation.reload

        expect(
          user.roles.where(organization: organization).map(&:role_type).sort
        ).to eq(%w[admin employee])
      end
    end

    context "when the invitation is expired" do
      before do
        invitation.update(expires_at: 1.day.ago)
      end

      specify do
        expect do
          invitation.claim!(user)
        end.to raise_error(Invitation::Expired)
      end
    end

    context "when the invitation is already claimed" do
      let(:other_user) { create(:user) }

      specify do
        invitation.claim!(user)

        expect do
          invitation.claim!(other_user)
        end.to raise_error(Invitation::AlreadyClaimed)
      end
    end
  end
end
