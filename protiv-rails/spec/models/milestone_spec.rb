# frozen_string_literal: true

require "rails_helper"

RSpec.describe Milestone, type: :model do
  specify do
    expect(create(:milestone)).to be_valid
  end

  describe "associations" do
    it { should belong_to(:job).optional }
    it { should have_one(:sub_pro_pay_pro_payable) }
    it { should have_many(:milestone_times) }
  end

  describe "enums" do
    it do
      should define_enum_for(:status).
        backed_by_column_of_type(:enum).
        with_values(
          pending: "pending",
          in_progress: "in_progress",
          completed: "completed",
          canceled: "canceled"
        )
    end
    it { should allow_values(:pending, :in_progress, :completed, :canceled).for(:status) }
  end

  describe "#percent_complete_derived" do
    let(:organization) { create(:organization) }
    let(:milestone) do
      create(
        :milestone,
        organization:,
        material_cost: Money.from_amount(100),
        material_budget: Money.from_amount(1000),
        status: :in_progress
      )
    end

    it "is derived from material usage" do
      catalog_item = create(:catalog_item, organization:)
      milestone_item = create(:milestone_item, milestone:, organization:, catalog_item:, quantity: 200.0)
      create(
        :item_allocation,
        milestone:,
        organization:,
        catalog_item:,
        item_quantity: 20.0,
        unit_cost_cents: 100,
        total_cost_cents: 2000.0
      )
      milestone.update!(tracked_milestone_item: milestone_item)
      expect(milestone.percent_complete).to eq(10.0)
    end

    it "uses the database percentage if present" do
      milestone.percent_complete = 50
      milestone.save
      expect(milestone.percent_complete).to eq(50.0)
      expect(milestone.percent_complete_hundredths).to eq(5000)
    end

    it "uses the status if completed" do
      milestone.status = :completed
      milestone.save
      expect(milestone.percent_complete).to eq(100.0)
    end
  end

  describe "#total budget and cost" do
    let(:milestone) do
      create(
        :milestone,
        material_budget: Money.from_amount(100),
        material_cost: Money.from_amount(50),
        labor_budget: Money.from_amount(200),
        labor_cost: Money.from_amount(100),
        equipment_budget: Money.from_amount(300),
        equipment_cost: Money.from_amount(150)
      )
    end

    it "total budget is the sum of material_budget, labor_budget, and equipment_budget" do
      expect(milestone.total_budget).to eq(Money.from_amount(600))
    end

    it "total cost is the sum of material_cost, labor_cost, and equipment_cost" do
      expect(milestone.total_costs).to eq(Money.from_amount(300))
    end

    it "budget status is under if total cost is less than total budget" do
      expect(milestone.budget).to eq({
        status: "under",
        percentage: 50.0
      })
    end

    it "budget status is near if total cost is greater than 90% of total budget" do
      milestone.material_cost = milestone.material_budget * 0.91
      milestone.labor_cost = milestone.labor_budget * 0.91
      milestone.equipment_cost = milestone.equipment_budget * 0.91

      expect(milestone.budget).to eq({
        status: "near",
        percentage: 91.0
      })
    end

    it "budget status is over if total cost is greater than total budget" do
      milestone.material_cost = milestone.material_budget * 1.1
      milestone.labor_cost = milestone.labor_budget * 1.1
      milestone.equipment_cost = milestone.equipment_budget * 1.1

      expect(milestone.budget).to eq({
        status: "over",
        percentage: 110.0
      })
    end
  end

  describe '#material_name' do
    let(:milestone) do
      create(
        :milestone,
        organization:,
        material_cost: Money.from_amount(100),
        material_budget: Money.from_amount(1000),
        status: :in_progress
      )
    end

    let(:organization) { create(:organization) }

    context 'with a milestone_item' do
      let(:catalog_item) do
        create(
          :catalog_item,
          name: "custom material"
        )
      end
      let!(:milestone_item) do
        create(
          :milestone_item,
          organization:,
          milestone:,
          catalog_item:
        )
      end

      specify do
        milestone.update(tracked_milestone_item: milestone_item)
        expect(milestone.progress_tracking_source.catalog_item).to eq(catalog_item)
      end

      specify 'with %complete' do
        milestone.update(percent_complete: 50)
        expect(milestone.progress_tracking_source).to be_instance_of(PercentComplete)
      end
    end
  end
end
