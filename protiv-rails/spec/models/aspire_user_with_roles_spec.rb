# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AspireUserWithRoles, type: :model do
  describe 'attributes' do
    subject { described_class.new }

    it { is_expected.to respond_to(:identity_id) }
    it { is_expected.to respond_to(:name) }
    it { is_expected.to respond_to(:email) }
    it { is_expected.to respond_to(:phone_country_code) }
    it { is_expected.to respond_to(:phone) }
    it { is_expected.to respond_to(:contact_id) }
    it { is_expected.to respond_to(:aspire_roles) }
    it { is_expected.to respond_to(:protiv_roles) }
    it { is_expected.to respond_to(:has_user) }
    it { is_expected.to respond_to(:user_id) }
  end

  describe 'default values' do
    subject { described_class.new }

    it 'sets default value for aspire_roles' do
      expect(subject.aspire_roles).to eq([])
    end

    it 'sets default value for protiv_roles' do
      expect(subject.protiv_roles).to eq([])
    end

    it 'sets default value for has_user' do
      expect(subject.has_user).to eq(false)
    end
  end

  describe '#id' do
    subject { described_class.new(identity_id: 123) }

    it 'returns the identity_id' do
      expect(subject.id).to eq(123)
    end
  end

  describe 'ActiveModel::Model compliance' do
    subject { described_class.new }

    it 'includes ActiveModel::Model' do
      expect(described_class.included_modules).to include(ActiveModel::Model)
    end

    it 'includes ActiveModel::Attributes' do
      expect(described_class.included_modules).to include(ActiveModel::Attributes)
    end

    it 'responds to persisted?' do
      expect(subject).to respond_to(:persisted?)
    end

    it 'is not persisted' do
      expect(subject.persisted?).to be_falsey
    end
  end
end
