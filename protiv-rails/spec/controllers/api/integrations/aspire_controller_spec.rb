# frozen_string_literal: true

require "rails_helper"

# Simple test to verify the controller's syntax
RSpec.describe Api::Integrations::AspireController, type: :controller do
  describe "POST #create" do
    let(:client_id) { "test_client_id" }
    let(:secret_key) { "test_secret_key" }
    let(:user) { double("User", organization: double("Organization")) }
    let(:integration) { double("Integration", save: true) }

    before do
      # Skip the require_admin_role check
      allow(controller).to receive(:require_admin_role).and_return(true)
      # Mock the current_user method
      allow(controller).to receive(:current_user).and_return(user)
      # Mock the Integration.find_or_initialize_by method
      allow(Integration).to receive(:find_or_initialize_by).and_return(integration)
    end

    it "creates a new integration" do
      # Set expectations on the integration object
      expect(integration).to receive(:client_id=).with(client_id)
      expect(integration).to receive(:secret=).with(secret_key)
      expect(integration).to receive(:save).and_return(true)

      # Make the request
      process :create, method: :post, params: { client_id: client_id, secret_key: secret_key }

      # Verify the response
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to eq({ "message" => "Aspire credentials saved." })
    end
  end
end
