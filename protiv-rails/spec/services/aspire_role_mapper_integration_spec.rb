# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AspireRoleMapper, type: :integration do
  let(:organization) { create(:organization) }
  let(:integration) { create(:integration, organization: organization, source: 'aspire') }
  let(:adapter) { instance_double(Sync::Aspire::Adapter) }

  # Sample data for Aspire roles
  let(:aspire_roles_data) do
    [
      { id: '1', name: 'Admin' },
      { id: '2', name: 'Manager' },
      { id: '3', name: '<PERSON> Lead' },
      { id: '4', name: 'Worker' }
    ]
  end

  # Sample data for user roles
  let(:user_roles_data) do
    [
      { user_id: '101', role_name: 'Admin' },
      { user_id: '102', role_name: 'Manager' },
      { user_id: '103', role_name: 'Crew Lead' },
      { user_id: '104', role_name: 'Worker' }
    ]
  end

  before do
    # Set up the adapter with test data
    allow(integration).to receive(:adapter).and_return(adapter)
    allow(adapter).to receive(:get_aspire_roles_data).and_return(aspire_roles_data)
    allow(adapter).to receive(:retrieve_user_roles).and_return(user_roles_data)
  end

  describe 'end-to-end role mapping workflow' do
    subject(:mapper) { described_class.new(integration: integration) }

    context 'when creating and managing role mappings' do
      it 'creates, retrieves, and deletes mappings correctly' do
        expect(AspireRoleMapping.count).to eq(0)

        # Step 1: Create mappings
        mapper.create_or_update_mapping('Admin', Role::ADMIN)
        mapper.create_or_update_mapping('Manager', Role::MANAGER)

        # Verify mappings were created in the database
        expect(AspireRoleMapping.count).to eq(2)
        expect(AspireRoleMapping.find_by(aspire_role_name: 'Admin').protiv_role_type).to eq(Role::ADMIN)
        expect(AspireRoleMapping.find_by(aspire_role_name: 'Manager').protiv_role_type).to eq(Role::MANAGER)

        # Step 2: Retrieve and verify mapped roles
        mapped_roles = mapper.mapped_aspire_roles

        # All roles should be returned, with mapped roles having protiv_role set
        expect(mapped_roles.size).to eq(4)
        expect(mapped_roles).to include(
          { aspire_id: '1', aspire_name: 'Admin', protiv_role: Role::ADMIN },
          { aspire_id: '2', aspire_name: 'Manager', protiv_role: Role::MANAGER }
        )

        # Unmapped roles should have nil protiv_role
        expect(mapped_roles).to include(
          { aspire_id: '3', aspire_name: 'Crew Lead', protiv_role: nil },
          { aspire_id: '4', aspire_name: 'Worker', protiv_role: nil }
        )

        # Step 3: Check for conflicts
        conflicts = mapper.detect_role_mapping_conflicts

        # There should be unmapped roles
        expect(conflicts[:unmapped_roles]).to contain_exactly('Crew Lead', 'Worker')

        # These unmapped roles are also in use (from user_roles_data)
        expect(conflicts[:required_unmapped_roles]).to contain_exactly('Crew Lead', 'Worker')

        # Step 4: Update a mapping
        mapper.create_or_update_mapping('Admin', Role::MANAGER)

        # Verify the mapping was updated
        expect(AspireRoleMapping.count).to eq(2) # Count should remain the same
        expect(AspireRoleMapping.find_by(aspire_role_name: 'Admin').protiv_role_type).to eq(Role::MANAGER)

        # Step 5: Delete a mapping
        mapper.delete_mapping('Manager')

        # Verify the mapping was deleted
        expect(AspireRoleMapping.count).to eq(1)
        expect(AspireRoleMapping.find_by(aspire_role_name: 'Manager')).to be_nil

        conflicts = mapper.detect_role_mapping_conflicts

        # Since our mock data doesn't include Manager in unmapped_roles,
        # we should only expect Crew Lead and Worker
        expect(conflicts[:unmapped_roles]).to contain_exactly('Crew Lead', 'Worker')
        expect(conflicts[:required_unmapped_roles]).to contain_exactly('Crew Lead', 'Worker')
      end
    end

    context 'when mapping user roles' do
      before do
        # Create some mappings
        AspireRoleMapping.create!(organization: organization, aspire_role_name: 'Admin', protiv_role_type: Role::ADMIN)
        AspireRoleMapping.create!(organization: organization, aspire_role_name: 'Manager', protiv_role_type: Role::MANAGER)
      end

      it 'correctly maps Aspire roles to Protiv roles' do
        mapped_roles = mapper.map_aspire_user_roles(%w[Admin Manager Unmapped])

        expect(mapped_roles).to contain_exactly(Role::ADMIN, Role::MANAGER)

        mapped_roles = mapper.map_aspire_user_roles(['Admin'])
        expect(mapped_roles).to contain_exactly(Role::ADMIN)

        mapped_roles = mapper.map_aspire_user_roles(['Unmapped'])
        expect(mapped_roles).to contain_exactly(Role::EMPLOYEE)
      end
    end
  end

  describe 'class methods' do
    context '.for_organization' do
      it 'creates a mapper for the given organization' do
        integration

        mapper = described_class.for_organization(organization)

        expect(mapper).to be_a(described_class)
        expect(mapper.instance_variable_get(:@integration)).to eq(integration)
        expect(mapper.instance_variable_get(:@organization)).to eq(organization)
      end

      it 'handles missing integration' do
        other_org = create(:organization)

        mapper = described_class.for_organization(other_org)

        expect(mapper).to be_a(described_class)
        expect(mapper.instance_variable_get(:@integration)).to be_nil

        expect { mapper.all_aspire_roles }.to raise_error(AspireRoleMapper::IntegrationNotFoundError)
      end
    end
  end
end
