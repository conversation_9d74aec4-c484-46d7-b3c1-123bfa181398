# frozen_string_literal: true

require "rails_helper"

RSpec.describe UpdateOrganizationUser do
  let(:organization) { create(:organization) }
  let(:user) { create(:user, organization: organization, role_type: "employee") }
  let(:role) { user.roles.find_by(organization: organization) }

  describe "#extract_role_attributes" do
    context "with role and user attributes" do
      subject(:service) do
        described_class.new(role: role, attributes: {
          active: false,
          role_type: "manager",
          wage: 50.0,
          user_name: "Should Not Be Included"
        })
      end

      subject(:role_attrs) { service.send(:extract_role_attributes) }

      it "includes the active attribute" do
        expect(role_attrs).to include(active: false)
      end

      it "includes the role_type attribute" do
        expect(role_attrs).to include(role_type: "manager")
      end

      it "includes the wage attribute" do
        expect(role_attrs).to include(wage: 50.0)
      end

      it "excludes user attributes" do
        expect(role_attrs).not_to have_key(:user_name)
      end
    end

    context "with only user attributes" do
      subject(:service) do
        described_class.new(role: role, attributes: {
          user_name: "Only User Attribute"
        })
      end

      subject(:role_attrs) { service.send(:extract_role_attributes) }

      it "returns an empty hash" do
        expect(role_attrs).to be_empty
      end
    end
  end

  describe "#extract_user_attributes" do
    context "with user attributes" do
      subject(:service) do
        described_class.new(role: role, attributes: {
          active: false,
          user_name: "Updated Name",
          user_email: "<EMAIL>",
          user_phone_country_code: "1",
          user_phone: "************"
        })
      end

      subject(:user_attrs) { service.send(:extract_user_attributes) }

      it "includes the name attribute" do
        expect(user_attrs).to include(name: "Updated Name")
      end

      it "includes the email attribute" do
        expect(user_attrs).to include(email: "<EMAIL>")
      end

      it "includes the phone_country_code attribute" do
        expect(user_attrs).to include(phone_country_code: "1")
      end

      it "includes the phone attribute" do
        expect(user_attrs).to include(phone: "************")
      end

      it "excludes role attributes" do
        expect(user_attrs).not_to have_key(:active)
      end
    end

    context "with only role attributes" do
      subject(:service) do
        described_class.new(role: role, attributes: {
          active: false,
          role_type: "manager"
        })
      end

      subject(:user_attrs) { service.send(:extract_user_attributes) }

      it "returns an empty hash" do
        expect(user_attrs).to be_empty
      end
    end
  end

  describe "#extract_phone_attributes" do
    context "with both phone attributes" do
      subject(:service) do
        described_class.new(role: role, attributes: {
          user_phone_country_code: "1",
          user_phone: "************"
        })
      end

      subject(:user_attrs) do
        attrs = {}
        service.send(:extract_phone_attributes, attrs)
        attrs
      end

      it "includes the phone_country_code attribute" do
        expect(user_attrs).to include(phone_country_code: "1")
      end

      it "includes the phone attribute" do
        expect(user_attrs).to include(phone: "************")
      end
    end

    context "with only phone attribute" do
      subject(:service) do
        described_class.new(role: role, attributes: {
          user_phone: "************"
        })
      end

      subject(:user_attrs) do
        attrs = {}
        service.send(:extract_phone_attributes, attrs)
        attrs
      end

      it "includes the phone attribute" do
        expect(user_attrs).to include(phone: "************")
      end

      it "does not include the phone_country_code attribute" do
        expect(user_attrs).not_to have_key(:phone_country_code)
      end
    end

    context "with no phone attributes" do
      subject(:service) do
        described_class.new(role: role, attributes: {
          user_name: "No Phone Info"
        })
      end

      subject(:user_attrs) do
        attrs = {}
        service.send(:extract_phone_attributes, attrs)
        attrs
      end

      it "returns an empty hash" do
        expect(user_attrs).to be_empty
      end
    end
  end

  describe "#execute" do
    context "when updating role attributes" do
      let(:attributes) do
        {
          active: false,
          role_type: "manager",
          wage: 50.0
        }
      end

      subject(:service) { described_class.new(role: role, attributes: attributes) }
      subject(:result) { service.execute }

      before do
        result
        role.reload
      end

      it "returns a successful result" do
        expect(result).to be_ok
      end

      it "returns the role as the state" do
        expect(result.state).to eq(role)
      end

      it "updates the active attribute" do
        expect(role.active).to be false
      end

      it "updates the role_type attribute" do
        expect(role.role_type).to eq("manager")
      end

      it "updates the wage attribute" do
        expect(role.wage).to eq(50.0)
      end
    end

    context "when updating user attributes" do
      let(:attributes) do
        {
          user_name: "Updated Name",
          user_email: "<EMAIL>",
          user_phone_country_code: "1",
          user_phone: "************"
        }
      end

      subject(:service) { described_class.new(role: role, attributes: attributes) }
      subject(:result) { service.execute }

      before do
        result
        user.reload
      end

      it "returns a successful result" do
        expect(result).to be_ok
      end

      it "returns the role as the state" do
        expect(result.state).to eq(role)
      end

      it "updates the name attribute" do
        expect(user.name).to eq("Updated Name")
      end

      it "updates the email attribute" do
        expect(user.email).to eq("<EMAIL>")
      end

      it "updates the phone_country_code attribute" do
        expect(user.phone_country_code).to eq("1")
      end

      it "updates the phone attribute" do
        expect(user.phone).to eq("************")
      end
    end

    context "when updating both role and user attributes" do
      let(:attributes) do
        {
          active: false,
          role_type: "manager",
          wage: 50.0,
          user_name: "Updated Name",
          user_email: "<EMAIL>"
        }
      end

      subject(:service) { described_class.new(role: role, attributes: attributes) }
      subject(:result) { service.execute }

      before do
        result
        role.reload
        user.reload
      end

      it "returns a successful result" do
        expect(result).to be_ok
      end

      it "returns the role as the state" do
        expect(result.state).to eq(role)
      end

      it "updates the role's active attribute" do
        expect(role.active).to be false
      end

      it "updates the role's role_type attribute" do
        expect(role.role_type).to eq("manager")
      end

      it "updates the role's wage attribute" do
        expect(role.wage).to eq(50.0)
      end

      it "updates the user's name attribute" do
        expect(user.name).to eq("Updated Name")
      end

      it "updates the user's email attribute" do
        expect(user.email).to eq("<EMAIL>")
      end
    end

    context "when there's an error" do
      let(:attributes) do
        {
          user_email: "invalid-email"
        }
      end

      subject(:service) { described_class.new(role: role, attributes: attributes) }
      subject(:result) { service.execute }

      it "returns an unsuccessful result" do
        expect(result).not_to be_ok
      end

      it "includes an error message" do
        expect(result.error).to be_present
      end
    end
  end
end
