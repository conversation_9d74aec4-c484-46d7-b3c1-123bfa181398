# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AspireUserCreator do
  let(:organization) { create(:organization) }

  subject(:user_creator) { described_class.new(organization: organization) }

  describe '#initialize' do
    it 'sets the organization' do
      expect(user_creator.organization).to eq(organization)
    end

    it 'initializes created_users as an empty array' do
      expect(user_creator.created_users).to eq([])
    end

    it 'initializes errors as an empty array' do
      expect(user_creator.errors).to eq([])
    end
  end

  describe '#create_users_for_identities' do
    context 'when no identity IDs are provided' do
      subject(:result) { user_creator.create_users_for_identities([]) }

      it 'returns an error message about no identity IDs' do
        expect(result[:errors]).to include("No identity IDs provided")
      end

      it 'returns an empty array for created_users' do
        expect(result[:created_users]).to eq([])
      end

      it 'returns an empty array for user_records' do
        expect(result[:user_records]).to eq([])
      end
    end

    context 'when identity IDs are provided' do
      let!(:identity1) { create(:identity, organization: organization, email: '<EMAIL>', name: 'Test User 1') }
      let!(:identity2) { create(:identity, organization: organization, email: '<EMAIL>', name: 'Test User 2') }
      let!(:identity3) { create(:identity, organization: organization, email: nil, name: 'Test User 3') }
      let!(:identity_with_user) { create(:identity, organization: organization, user: create(:user)) }
      let!(:other_org_identity) { create(:identity, organization: create(:organization)) }

      let(:identity_ids) { [identity1.id, identity2.id, identity3.id, identity_with_user.id, other_org_identity.id] }
      let(:identity_data) { { identity1.id.to_s => { email: '<EMAIL>', name: 'Custom Name 1' } } }

      subject(:result) { user_creator.create_users_for_identities(identity_ids, identity_data) }

      it 'only processes identities that belong to the organization and have no user' do
        expect(Identity).to receive(:where).with(
          id: identity_ids,
          organization_id: organization.id,
          user_id: nil
        ).and_call_original

        result
      end

      it 'creates users for valid identities' do
        expect { result }.to change(User, :count).by(2)
      end

      it 'returns the created user records' do
        expect(result[:user_records].size).to eq(2)
      end

      it 'adds an error for identities without email' do
        expect(result[:errors]).to include(hash_including(identity_id: identity3.id, error: "Email is required to create a user"))
      end

      it 'uses custom email from identity_data when provided' do
        result
        user = User.find_by(email: '<EMAIL>')
        expect(user).not_to be_nil
      end

      it 'uses custom name from identity_data when provided' do
        result
        user = User.find_by(email: '<EMAIL>')
        expect(user.name).to eq('Custom Name 1')
      end

      it 'uses identity email when no custom email is provided' do
        result
        user = User.find_by(email: '<EMAIL>')
        expect(user).not_to be_nil
      end

      it 'uses identity name when no custom name is provided' do
        result
        user = User.find_by(email: '<EMAIL>')
        expect(user.name).to eq('Test User 2')
      end

      it 'links the identity to the created user' do
        result
        expect(identity1.reload.user).not_to be_nil
        expect(identity2.reload.user).not_to be_nil
      end
    end
  end

  describe '#create_user_for_identity (private method)' do
    let(:identity) { create(:identity, organization: organization, email: '<EMAIL>', name: 'Test User') }
    let(:data) { { email: '<EMAIL>', name: 'Custom Name' } }

    subject(:create_user) { user_creator.send(:create_user_for_identity, identity, data) }

    context 'when all required data is present' do
      it 'creates a new user' do
        expect { create_user }.to change(User, :count).by(1)
      end

      it 'returns the created user' do
        expect(create_user).to be_a(User)
      end

      it 'links the identity to the user' do
        user = create_user
        expect(identity.reload.user_id).to eq(user.id)
      end
    end

    context 'when email is missing' do
      let(:identity_without_email) { create(:identity, organization: organization, email: nil, name: 'No Email') }

      subject(:create_user_without_email) { user_creator.send(:create_user_for_identity, identity_without_email, {}) }

      it 'does not create a user' do
        expect { create_user_without_email }.not_to change(User, :count)
      end

      it 'returns nil' do
        expect(create_user_without_email).to be_nil
      end

      it 'adds an error to the errors array' do
        create_user_without_email
        expect(user_creator.errors).to include(hash_including(identity_id: identity_without_email.id, error: "Email is required to create a user"))
      end
    end

    context 'when user creation fails' do
      before do
        allow_any_instance_of(User).to receive(:save).and_return(false)
        allow_any_instance_of(User).to receive(:errors).and_return(double(full_messages: ['Email is invalid']))
      end

      it 'does not create a user' do
        expect { create_user }.not_to change(User, :count)
      end

      it 'returns nil' do
        expect(create_user).to be_nil
      end

      it 'adds an error to the errors array' do
        create_user
        expect(user_creator.errors).to include(hash_including(identity_id: identity.id, error: "Failed to create user: Email is invalid"))
      end
    end

    context 'when an exception occurs' do
      before do
        allow_any_instance_of(User).to receive(:save).and_raise(StandardError.new("Test error"))
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with(/Error creating user for identity #{identity.id}: Test error/)
        expect(Rails.logger).to receive(:error)
        create_user
      end

      it 'adds an error to the errors array' do
        create_user
        expect(user_creator.errors).to include(hash_including(identity_id: identity.id, error: "Exception: Test error"))
      end

      it 'returns nil' do
        expect(create_user).to be_nil
      end
    end
  end
end
