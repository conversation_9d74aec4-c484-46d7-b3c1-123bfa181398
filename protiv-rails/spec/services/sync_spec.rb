# frozen_string_literal: true

require 'rails_helper'

RSpec.describe "syncing modules" do
  shared_examples :sync_module_behavior do
    let(:sync_module) { described_class }

    it 'describes capabilities' do
      expect(sync_module.capabilities.keys.sort).to eq(%i[
        jobs
        milestones
        milestone_items
        milestone_times
        identities
        branches
        routes
        clock_times
        catalog_items
        services
        item_allocations
      ].sort)
    end

    it 'has a materializer' do
      expect(sync_module.materializer).to be_present
    end

    it 'has a source' do
      expect(sync_module.source).to be_instance_of(String)
    end

    it 'has an adapter' do
      expect(sync_module.adapter).to be_present
    end

    it 'is registered' do
      expect(::Sync.lookup(sync_module.source)).to eq(sync_module)
    end

    it 'can convert a cache to a resource' do
      expect(sync_module).to respond_to(:cache_to_resource)
    end

    it 'can query for create' do
      expect(sync_module).to respond_to(:can_create?)
    end

    it_behaves_like(:sync_adapter_behavior)
  end

  shared_examples(:sync_adapter_behavior) do
    let(:adapter) { sync_module.adapter }
    let(:adapter_instance) { sync_module.adapter.new(double("integration")) }

    it 'has remote helper models' do
      expect(adapter_instance.remote_helper_models).to be_instance_of(Array)
    end

    it 'refreshes credentials' do
      expect(adapter_instance).to respond_to(:refresh_credential)
    end

    it 'retrieves credentials' do
      expect(adapter_instance).to respond_to(:retrieve_credential)
    end

    it 'can retrieve the appropriate resources' do
      sync_module.capabilities.each do |kind, actions|
        if actions.include?(:pull)
          expect(adapter_instance).to respond_to("retrieve_#{kind.to_s.pluralize}")
        end
      end
    end

    it 'can create the appropriate resources' do
      sync_module.capabilities.each do |kind, actions|
        if actions.include?(:create)
          expect(adapter_instance).to respond_to("create_#{kind.to_s.singularize}")
        end
      end
    end

    it 'can update the appropriate resources' do
      sync_module.capabilities.each do |kind, actions|
        if actions.include?(:update)
          expect(adapter_instance).to respond_to("update_#{kind.to_s.singularize}")
        end
      end
    end
  end

  describe ::Sync::Aspire do
    it_behaves_like(:sync_module_behavior)
  end

  describe ::SpecHelpers::Sync::TestA do
    it_behaves_like(:sync_module_behavior)
  end

  describe ::SpecHelpers::Sync::TestB do
    it_behaves_like(:sync_module_behavior)
  end
end
