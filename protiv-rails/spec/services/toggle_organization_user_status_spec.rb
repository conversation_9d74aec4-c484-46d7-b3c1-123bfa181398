# frozen_string_literal: true

require "rails_helper"

RSpec.describe ToggleOrganizationUserStatus do
  let(:organization) { create(:organization) }
  let(:user) { create(:user, organization: organization, role_type: "employee") }
  let(:role) { user.roles.find_by(organization: organization) }

  describe "#execute" do
    context "when changing to active status" do
      before do
        role.update(active: false, status: "inactive")
      end

      subject(:result) { described_class.new(role: role, status: "active").execute }

      before do
        result
        role.reload
      end

      it "returns a successful result" do
        expect(result).to be_ok
      end

      it "returns the role as the state" do
        expect(result.state).to eq(role)
      end

      it "activates the role" do
        expect(role.active).to be true
      end

      it "sets the status to active" do
        expect(role.status).to eq("active")
      end

      it "sets the active_at timestamp" do
        expect(role.active_at).to be_present
      end
    end

    context "when changing to inactive status" do
      subject(:result) { described_class.new(role: role, status: "inactive").execute }

      before do
        result
        role.reload
      end

      it "returns a successful result" do
        expect(result).to be_ok
      end

      it "returns the role as the state" do
        expect(result.state).to eq(role)
      end

      it "deactivates the role" do
        expect(role.active).to be false
      end

      it "sets the status to inactive" do
        expect(role.status).to eq("inactive")
      end

      it "sets the inactive_at timestamp" do
        expect(role.inactive_at).to be_present
      end
    end

    context "when changing to invited status" do
      subject(:result) { described_class.new(role: role, status: "invited").execute }

      before do
        result
        role.reload
      end

      it "returns a successful result" do
        expect(result).to be_ok
      end

      it "returns the role as the state" do
        expect(result.state).to eq(role)
      end

      it "keeps the role active" do
        expect(role.active).to be true
      end

      it "sets the status to invited" do
        expect(role.status).to eq("invited")
      end
    end

    context "when changing to uninvited status" do
      subject(:result) { described_class.new(role: role, status: "uninvited").execute }

      before do
        result
        role.reload
      end

      it "returns a successful result" do
        expect(result).to be_ok
      end

      it "returns the role as the state" do
        expect(result.state).to eq(role)
      end

      it "keeps the role active" do
        expect(role.active).to be true
      end

      it "sets the status to uninvited" do
        expect(role.status).to eq("uninvited")
      end
    end

    context "when providing an invalid status" do
      subject(:result) { described_class.new(role: role, status: "invalid").execute }

      it "returns an unsuccessful result" do
        expect(result).not_to be_ok
      end

      it "includes an error message about invalid status" do
        expect(result.error.message).to include("Invalid status")
      end
    end

    context "when there's an error" do
      before do
        allow(role).to receive(:update!).and_raise(StandardError.new("Test error"))
      end

      subject(:result) { described_class.new(role: role, status: "active").execute }

      it "returns an unsuccessful result" do
        expect(result).not_to be_ok
      end

      it "includes the error message" do
        expect(result.error.message).to eq("Test error")
      end
    end
  end
end
