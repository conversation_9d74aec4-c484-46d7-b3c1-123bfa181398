# frozen_string_literal: true

require "rails_helper"

RSpec.describe OrganizationUserPolicy do
  let(:organization) { create(:organization) }
  let(:admin_user) { create(:user, organization: organization, role_type: "admin") }
  let(:manager_user) { create(:user, organization: organization, role_type: "manager") }
  let(:employee_user) { create(:user, organization: organization, role_type: "employee") }
  let(:other_org_user) { create(:user) }
  
  let(:role) { employee_user.roles.find_by(organization: organization) }
  
  describe "for admin users" do
    let(:policy) { described_class.new(admin_user, role) }
    
    before do
      Current.organization = organization
    end
    
    it { expect(policy).to permit(:index) }
    it { expect(policy).to permit(:show) }
    it { expect(policy).to permit(:create) }
    it { expect(policy).to permit(:update) }
    it { expect(policy).not_to permit(:destroy) }
  end
  
  describe "for manager users" do
    let(:policy) { described_class.new(manager_user, role) }
    
    before do
      Current.organization = organization
    end
    
    it { expect(policy).to permit(:index) }
    it { expect(policy).to permit(:show) }
    it { expect(policy).not_to permit(:create) }
    it { expect(policy).not_to permit(:update) }
    it { expect(policy).not_to permit(:destroy) }
  end
  
  describe "for employee users" do
    let(:policy) { described_class.new(employee_user, role) }
    
    before do
      Current.organization = organization
    end
    
    it { expect(policy).to permit(:index) }
    it { expect(policy).to permit(:show) }
    it { expect(policy).not_to permit(:create) }
    it { expect(policy).not_to permit(:update) }
    it { expect(policy).not_to permit(:destroy) }
  end
  
  describe "for users from other organizations" do
    let(:policy) { described_class.new(other_org_user, role) }
    
    before do
      Current.organization = organization
    end
    
    it { expect(policy).not_to permit(:index) }
    it { expect(policy).not_to permit(:show) }
    it { expect(policy).not_to permit(:create) }
    it { expect(policy).not_to permit(:update) }
    it { expect(policy).not_to permit(:destroy) }
  end
  
  describe "Scope" do
    subject { described_class::Scope.new(current_user, Role).resolve }
    
    before do
      Current.organization = organization
    end
    
    context "with admin user" do
      let(:current_user) { admin_user }
      
      it "includes all roles in the organization" do
        expect(subject).to include(role)
      end
    end
    
    context "with user from another organization" do
      let(:current_user) { other_org_user }
      
      it "returns no roles" do
        expect(subject).to be_empty
      end
    end
    
    context "with no user" do
      let(:current_user) { nil }
      
      it "returns no roles" do
        expect(subject).to be_empty
      end
    end
  end
end
