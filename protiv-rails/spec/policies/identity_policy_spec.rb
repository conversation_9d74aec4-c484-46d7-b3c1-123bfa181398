# frozen_string_literal: true

require 'rails_helper'

RSpec.describe IdentityPolicy do
  let(:organization) { create(:organization) }
  let(:other_organization) { create(:organization) }

  let(:admin_user) { create(:user) }
  let(:regular_user) { create(:user) }
  let(:other_user) { create(:user) }

  let(:admin_identity) { create(:identity, user: admin_user, organization: organization) }
  let(:user_identity) { create(:identity, user: regular_user, organization: organization) }
  let(:org_identity) { create(:identity, organization: organization) }
  let(:other_org_identity) { create(:identity, organization: other_organization) }

  before do
    create(:role, user: admin_user, organization: organization, role_type: Role::ADMIN)
    create(:role, user: regular_user, organization: organization, role_type: Role::EMPLOYEE)
    allow(Current).to receive(:organization).and_return(organization)
  end

  describe '#update?' do
    it 'allows anyone to update identities' do
      expect(IdentityPolicy.new(admin_user, admin_identity)).to permit(:update)
      expect(IdentityPolicy.new(regular_user, user_identity)).to permit(:update)
      expect(IdentityPolicy.new(other_user, org_identity)).to permit(:update)
      expect(IdentityPolicy.new(nil, org_identity)).to permit(:update)
    end
  end

  describe 'Scope' do
    let!(:admin_identity) { create(:identity, user: admin_user, organization: organization) }
    let!(:user_identity) { create(:identity, user: regular_user, organization: organization) }
    let!(:org_identity) { create(:identity, organization: organization) }
    let!(:other_org_identity) { create(:identity, organization: other_organization) }
    let!(:user_without_org_identity) { create(:identity, user: regular_user) }
    let!(:admin_without_org_identity) { create(:identity, user: admin_user) }

    context 'when user and organization are present' do
      subject { IdentityPolicy::Scope.new(admin_user, Identity).resolve }

      it 'returns identities for the current organization' do
        expect(subject).to include(admin_identity, user_identity, org_identity)
      end

      it 'excludes identities from other organizations' do
        expect(subject).not_to include(other_org_identity)
      end

      it 'excludes identities without organization even if they belong to the user' do
        expect(subject).not_to include(admin_without_org_identity)
      end
    end

    context 'when only user is present (no organization)' do
      before do
        allow(Current).to receive(:organization).and_return(nil)
      end

      subject { IdentityPolicy::Scope.new(regular_user, Identity).resolve }

      it 'returns identities belonging to the user' do
        expect(subject).to include(user_identity, user_without_org_identity)
      end

      it 'excludes identities from other users' do
        expect(subject).not_to include(admin_identity, admin_without_org_identity)
      end

      it 'excludes identities without a user' do
        expect(subject).not_to include(org_identity, other_org_identity)
      end
    end

    context 'when neither user nor organization is present' do
      before do
        allow(Current).to receive(:organization).and_return(nil)
      end

      subject { IdentityPolicy::Scope.new(nil, Identity).resolve }

      it 'returns no identities' do
        expect(subject).to be_empty
      end
    end
  end

  describe 'with permit matcher' do
    subject { described_class.new(user, identity) }

    context 'when user is nil' do
      let(:user) { nil }
      let(:identity) { create(:identity) }

      it { is_expected.to permit(:update) }
    end

    context 'when user is the identity owner' do
      let(:user) { regular_user }
      let(:identity) { user_identity }

      it { is_expected.to permit(:update) }
    end

    context 'when user is an admin' do
      let(:user) { admin_user }
      let(:identity) { org_identity }

      it { is_expected.to permit(:update) }
    end

    context 'when user is not related to the identity' do
      let(:user) { other_user }
      let(:identity) { org_identity }

      it { is_expected.to permit(:update) }
    end
  end
end
