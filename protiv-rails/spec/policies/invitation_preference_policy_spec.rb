# frozen_string_literal: true

require 'rails_helper'

RSpec.describe InvitationPreferencePolicy do
  let(:organization) { create(:organization) }
  let(:other_organization) { create(:organization) }

  let(:admin_user) { create(:user) }
  let(:manager_user) { create(:user) }
  let(:regular_user) { create(:user) }

  let(:invitation_preference) { organization.invitation_preferences.first }
  let(:other_org_preference) { other_organization.invitation_preferences.first }

  before do
    create(:role, user: admin_user, organization: organization, role_type: Role::ADMIN)
    create(:role, user: manager_user, organization: organization, role_type: Role::MANAGER)
    create(:role, user: regular_user, organization: organization, role_type: Role::EMPLOYEE)
    allow(Current).to receive(:organization).and_return(organization)
  end

  subject { described_class }

  describe 'policy methods' do
    context 'when user is an admin of the organization' do
      subject { described_class.new(admin_user, invitation_preference) }

      it { is_expected.to permit(:index) }
      it { is_expected.to permit(:show) }
      it { is_expected.to permit(:create) }
      it { is_expected.to permit(:update) }
    end

    context 'when user is a manager of the organization' do
      subject { described_class.new(manager_user, invitation_preference) }

      it { is_expected.not_to permit(:index) }
      it { is_expected.not_to permit(:show) }
      it { is_expected.not_to permit(:create) }
      it { is_expected.not_to permit(:update) }
    end

    context 'when user is a regular employee of the organization' do
      subject { described_class.new(regular_user, invitation_preference) }

      it { is_expected.not_to permit(:index) }
      it { is_expected.not_to permit(:show) }
      it { is_expected.not_to permit(:create) }
      it { is_expected.not_to permit(:update) }
    end

    context 'when user is an admin of a different organization' do
      let(:other_admin) { create(:user) }

      before do
        create(:role, user: other_admin, organization: other_organization, role_type: Role::ADMIN)
      end

      subject { described_class.new(other_admin, invitation_preference) }

      it { is_expected.not_to permit(:index) }
      it { is_expected.not_to permit(:show) }
      it { is_expected.not_to permit(:create) }
      it { is_expected.not_to permit(:update) }
    end
  end

  describe 'Scope' do
    let!(:preference1) { organization.invitation_preferences.first }
    let!(:preference2) { organization.invitation_preferences.second }
    let!(:other_org_preference) { other_organization.invitation_preferences.first }

    context 'when user is an admin of the organization' do
      subject { described_class::Scope.new(admin_user, InvitationPreference).resolve }

      it 'includes preferences for the current organization' do
        expect(subject).to include(preference1, preference2)
      end

      it 'excludes preferences for other organizations' do
        expect(subject).not_to include(other_org_preference)
      end
    end

    context 'when user is not an admin of the organization' do
      subject { described_class::Scope.new(regular_user, InvitationPreference).resolve }

      it 'returns no preferences' do
        expect(subject).to be_empty
      end
    end

    context 'when user is nil' do
      subject { described_class::Scope.new(nil, InvitationPreference).resolve }

      it 'returns no preferences' do
        expect(subject).to be_empty
      end
    end
  end

  describe 'individual policy methods' do
    describe '#index?' do
      it 'allows access for organization admins' do
        policy = described_class.new(admin_user, invitation_preference)
        expect(policy.index?).to be true
      end

      it 'denies access for non-admins' do
        policy = described_class.new(regular_user, invitation_preference)
        expect(policy.index?).to be false
      end
    end

    describe '#show?' do
      it 'allows access for organization admins' do
        policy = described_class.new(admin_user, invitation_preference)
        expect(policy.show?).to be true
      end

      it 'denies access for non-admins' do
        policy = described_class.new(regular_user, invitation_preference)
        expect(policy.show?).to be false
      end
    end

    describe '#create?' do
      it 'allows access for organization admins' do
        policy = described_class.new(admin_user, invitation_preference)
        expect(policy.create?).to be true
      end

      it 'denies access for non-admins' do
        policy = described_class.new(regular_user, invitation_preference)
        expect(policy.create?).to be false
      end
    end

    describe '#update?' do
      it 'allows access for organization admins' do
        policy = described_class.new(admin_user, invitation_preference)
        expect(policy.update?).to be true
      end

      it 'denies access for non-admins' do
        policy = described_class.new(regular_user, invitation_preference)
        expect(policy.update?).to be false
      end
    end
  end
end
