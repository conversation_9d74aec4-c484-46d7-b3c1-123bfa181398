# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AspireRoleMappingResource, type: :resource do
  let(:organization) { create(:organization) }
  let(:user) { create(:user) }
  let(:role_mapping) { create(:aspire_role_mapping, organization: organization) }

  before do
    create(:role, user: user, organization: organization, role_type: Role::ADMIN)
    allow(Current).to receive(:organization).and_return(organization)
    allow(Current).to receive(:user).and_return(user)
  end

  it 'has the correct attributes' do
    expect(described_class).to have_attribute(:aspire_role_name, :string)
    expect(described_class).to have_attribute(:protiv_role_type, :string)
  end

  it 'has the correct relationships' do
    expect(described_class).to have_relationship(:organization)
  end

  describe 'filters' do
    it 'can filter by organization_id' do
      other_org = create(:organization)
      mapping1 = create(:aspire_role_mapping, organization: organization)
      mapping2 = create(:aspire_role_mapping, organization: organization)
      other_mapping = create(:aspire_role_mapping, organization: other_org)

      params = { filter: { organization_id: organization.id.to_s } }
      results = described_class.all(params).data

      expect(results.map(&:id)).to include(mapping1.id, mapping2.id)
      expect(results.map(&:id)).not_to include(other_mapping.id)
    end
  end

  describe 'creating' do
    let(:payload) do
      {
        data: {
          type: 'aspire_role_mappings',
          attributes: {
            aspire_role_name: 'Branch Manager',
            protiv_role_type: Role::MANAGER
          },
          relationships: {
            organization: {
              data: {
                id: organization.id.to_s,
                type: 'organizations'
              }
            }
          }
        }
      }
    end

    it 'works' do
      expect {
        described_class.build(payload)
      }.to change(AspireRoleMapping, :count).by(1)

      mapping = AspireRoleMapping.last
      expect(mapping.aspire_role_name).to eq('Branch Manager')
      expect(mapping.protiv_role_type).to eq(Role::MANAGER)
      expect(mapping.organization_id).to eq(organization.id)
    end

    context 'with invalid protiv_role_type' do
      let(:invalid_payload) do
        {
          data: {
            type: 'aspire_role_mappings',
            attributes: {
              aspire_role_name: 'Branch Manager',
              protiv_role_type: 'invalid_role'
            },
            relationships: {
              organization: {
                data: {
                  id: organization.id.to_s,
                  type: 'organizations'
                }
              }
            }
          }
        }
      end

      it 'raises an error' do
        expect {
          described_class.build(invalid_payload)
        }.to raise_error(Graphiti::Errors::InvalidRequest, /protiv_role_type invalid_role is not a valid role type/)
      end
    end
  end

  describe 'updating' do
    let!(:mapping) { create(:aspire_role_mapping,
                           organization: organization,
                           aspire_role_name: 'Branch Admin',
                           protiv_role_type: Role::ADMIN) }

    let(:update_payload) do
      {
        data: {
          id: mapping.id.to_s,
          type: 'aspire_role_mappings',
          attributes: {
            protiv_role_type: Role::MANAGER
          }
        }
      }
    end

    it 'updates the mapping' do
      expect {
        described_class.find(update_payload)
      }.to change { mapping.reload.protiv_role_type }.from(Role::ADMIN).to(Role::MANAGER)
    end

    context 'with invalid protiv_role_type' do
      let(:invalid_update_payload) do
        {
          data: {
            id: mapping.id.to_s,
            type: 'aspire_role_mappings',
            attributes: {
              protiv_role_type: 'not_a_valid_role'
            }
          }
        }
      end

      it 'raises an error' do
        expect {
          described_class.find(invalid_update_payload)
        }.to raise_error(Graphiti::Errors::InvalidRequest, /protiv_role_type not_a_valid_role is not a valid role type/)
      end
    end
  end

  describe 'destroying' do
    let!(:mapping) { create(:aspire_role_mapping, organization: organization) }

    let(:destroy_payload) do
      {
        data: {
          id: mapping.id.to_s,
          type: 'aspire_role_mappings'
        }
      }
    end

    it 'destroys the mapping' do
      expect {
        described_class.find(destroy_payload).destroy
      }.to change(AspireRoleMapping, :count).by(-1)

      expect(AspireRoleMapping.exists?(mapping.id)).to be false
    end
  end

  describe 'primary_endpoint' do
    it 'has the correct endpoint namespace' do
      expect(described_class.endpoint_namespace).to eq('/integrations/aspire/role_mappings')
    end

    it 'has the correct endpoint actions' do
      expect(described_class.endpoint_actions).to contain_exactly(:index, :create)
    end
  end

  describe 'validate_protiv_role_type' do
    let(:resource) { described_class.new }

    it 'accepts valid role types' do
      [Role::ADMIN, Role::MANAGER, Role::EMPLOYEE, 'crew_lead'].each do |role_type|
        expect { resource.send(:validate_protiv_role_type, role_type) }.not_to raise_error
      end
    end

    it 'rejects invalid role types' do
      ['invalid', 'not_a_role', ''].each do |role_type|
        expect {
          resource.send(:validate_protiv_role_type, role_type)
        }.to raise_error(Graphiti::Errors::InvalidRequest)
      end
    end
  end

  describe 'serialization' do
    let!(:mapping) { create(:aspire_role_mapping,
                           organization: organization,
                           aspire_role_name: 'Branch Admin',
                           protiv_role_type: Role::ADMIN) }

    it 'serializes the correct attributes' do
      params = { filter: { id: mapping.id.to_s } }
      serialized = described_class.all(params).data.first

      expect(serialized.id).to eq(mapping.id.to_s)
      expect(serialized.aspire_role_name).to eq('Branch Admin')
      expect(serialized.protiv_role_type).to eq(Role::ADMIN)
      expect(serialized.organization_id).to eq(organization.id)
    end

    it 'includes organization when requested' do
      params = {
        filter: { id: mapping.id.to_s },
        include: 'organization'
      }
      result = described_class.all(params)

      expect(result.included.map(&:jsonapi_type).uniq).to contain_exactly('organizations')
      expect(result.included.first.id).to eq(organization.id.to_s)
    end
  end
end
