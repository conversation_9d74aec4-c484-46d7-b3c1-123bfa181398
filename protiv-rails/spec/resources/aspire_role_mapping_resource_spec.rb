# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AspireRoleMappingResource, type: :resource do
  let(:organization) { create(:organization) }
  let(:user) { create(:user) }
  
  before do
    create(:role, user: user, organization: organization, role_type: Role::ADMIN)
    allow(Current).to receive(:organization).and_return(organization)
    allow(Current).to receive(:user).and_return(user)
  end

  describe 'creating' do
    let(:payload) do
      {
        data: {
          type: 'aspire_role_mappings',
          attributes: {
            aspire_role_name: 'Branch Admin',
            protiv_role_type: Role::ADMIN
          },
          relationships: {
            organization: {
              data: {
                id: organization.id.to_s,
                type: 'organizations'
              }
            }
          }
        }
      }
    end

    it 'creates a new aspire role mapping' do
      expect {
        described_class.build(payload)
      }.to change { AspireRoleMapping.count }.by(1)
      
      aspire_role_mapping = AspireRoleMapping.last
      expect(aspire_role_mapping.aspire_role_name).to eq('Branch Admin')
      expect(aspire_role_mapping.protiv_role_type).to eq(Role::ADMIN)
      expect(aspire_role_mapping.organization_id).to eq(organization.id)
    end

    context 'with invalid protiv_role_type' do
      let(:payload) do
        {
          data: {
            type: 'aspire_role_mappings',
            attributes: {
              aspire_role_name: 'Branch Admin',
              protiv_role_type: 'invalid_role'
            },
            relationships: {
              organization: {
                data: {
                  id: organization.id.to_s,
                  type: 'organizations'
                }
              }
            }
          }
        }
      end

      it 'raises an error' do
        expect {
          described_class.build(payload)
        }.to raise_error(Graphiti::Errors::InvalidRequest, /protiv_role_type invalid_role is not a valid role type/)
      end
    end
  end

  describe 'updating' do
    let!(:aspire_role_mapping) { create(:aspire_role_mapping, organization: organization, aspire_role_name: 'Branch Admin', protiv_role_type: Role::ADMIN) }
    
    let(:payload) do
      {
        data: {
          id: aspire_role_mapping.id.to_s,
          type: 'aspire_role_mappings',
          attributes: {
            protiv_role_type: Role::MANAGER
          }
        }
      }
    end

    it 'updates an existing aspire role mapping' do
      expect {
        described_class.find(payload)
      }.to change { aspire_role_mapping.reload.protiv_role_type }.from(Role::ADMIN).to(Role::MANAGER)
    end

    context 'with invalid protiv_role_type' do
      let(:payload) do
        {
          data: {
            id: aspire_role_mapping.id.to_s,
            type: 'aspire_role_mappings',
            attributes: {
              protiv_role_type: 'invalid_role'
            }
          }
        }
      end

      it 'raises an error' do
        expect {
          described_class.find(payload)
        }.to raise_error(Graphiti::Errors::InvalidRequest, /protiv_role_type invalid_role is not a valid role type/)
      end
    end
  end

  describe 'filtering' do
    let!(:aspire_role_mapping1) { create(:aspire_role_mapping, organization: organization) }
    let!(:aspire_role_mapping2) { create(:aspire_role_mapping, organization: organization) }
    let!(:other_org) { create(:organization) }
    let!(:other_org_mapping) { create(:aspire_role_mapping, organization: other_org) }

    context 'by organization_id' do
      it 'returns mappings for the specified organization' do
        params = { filter: { organization_id: organization.id.to_s } }
        results = described_class.all(params).data
        
        expect(results.map(&:id)).to contain_exactly(aspire_role_mapping1.id, aspire_role_mapping2.id)
        expect(results.map(&:id)).not_to include(other_org_mapping.id)
      end
    end
  end

  describe 'fields' do
    let!(:aspire_role_mapping) { create(:aspire_role_mapping, organization: organization, aspire_role_name: 'Branch Admin', protiv_role_type: Role::ADMIN) }
    
    it 'has the correct readable attributes' do
      params = { filter: { id: aspire_role_mapping.id.to_s } }
      result = described_class.all(params).data.first
      
      expect(result.aspire_role_name).to eq('Branch Admin')
      expect(result.protiv_role_type).to eq(Role::ADMIN)
      expect(result.organization_id).to eq(organization.id)
    end
  end

  describe 'primary_endpoint' do
    it 'sets the correct primary endpoint' do
      expect(described_class.endpoint_namespace).to eq('/integrations/aspire/role_mappings')
      expect(described_class.endpoint_actions).to contain_exactly(:index, :create)
    end
  end

  describe 'relationships' do
    let!(:aspire_role_mapping) { create(:aspire_role_mapping, organization: organization) }
    
    it 'includes organization relationship' do
      params = { 
        filter: { id: aspire_role_mapping.id.to_s },
        include: 'organization'
      }
      result = described_class.all(params)
      
      expect(result.included.map(&:jsonapi_type).uniq).to contain_exactly('organizations')
      expect(result.included.map(&:id)).to contain_exactly(organization.id.to_s)
    end
  end

  describe 'validation' do
    describe '#validate_protiv_role_type' do
      let(:resource) { described_class.new }
      
      it 'allows valid role types' do
        valid_types = [Role::ADMIN, Role::MANAGER, Role::EMPLOYEE, "crew_lead"]
        
        valid_types.each do |role_type|
          expect { resource.send(:validate_protiv_role_type, role_type) }.not_to raise_error
        end
      end
      
      it 'raises error for invalid role types' do
        invalid_types = ['invalid', 'super_admin', 'user']
        
        invalid_types.each do |role_type|
          expect { 
            resource.send(:validate_protiv_role_type, role_type) 
          }.to raise_error(Graphiti::Errors::InvalidRequest, /protiv_role_type #{role_type} is not a valid role type/)
        end
      end
      
      it 'allows nil role type' do
        expect { resource.send(:validate_protiv_role_type, nil) }.not_to raise_error
      end
    end
  end
end