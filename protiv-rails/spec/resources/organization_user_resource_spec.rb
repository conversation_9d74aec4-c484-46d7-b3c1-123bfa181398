# frozen_string_literal: true

require "rails_helper"

RSpec.describe OrganizationUserResource, type: :resource do
  let(:organization) { create(:organization) }
  let(:current_user) { create(:user, organization: organization, role_type: "admin") }
  let!(:other_users) { create_list(:user, 3, organization: organization, role_type: "employee") }

  before do
    allow_any_instance_of(OrganizationUserPolicy::Scope).to receive(:resolve) do |instance|
      instance.send(:scope).where(organization: organization)
    end

    Graphiti.with_context(OpenStruct.new(current_user: current_user, current_organization: organization)) do
      Role.where.not(id: Role.where(organization: organization).pluck(:id)).delete_all
    end
  end

  def with_user_context(&block)
    Current.organization = organization
    Graphiti.with_context(OpenStruct.new(current_user: current_user, current_organization: organization), &block)
  ensure
    Current.organization = nil
  end

  def with_no_user_context(&block)
    Current.organization = nil
    Graphiti.with_context(OpenStruct.new(current_user: nil, current_organization: nil), &block)
  end

  context "with current_user" do
    subject(:all_users) { with_user_context { OrganizationUserResource.all.data } }

    it "includes all users in the organization" do
      expect(all_users.length).to eq(4)
    end

    it "returns Role objects" do
      expect(all_users.first).to be_a(Role)
    end
  end

  context "with no user" do
    subject(:no_user_data) { with_no_user_context { OrganizationUserResource.all.data } }

    it "returns no data when no user is present" do
      expect(no_user_data.length).to eq(0)
    end
  end

  describe "filtering" do
    let!(:inactive_user) { create(:user, organization: organization, role_type: "employee") }

    before do
      inactive_user.roles.find_by(organization: organization).update(active: false, status: "inactive")
    end

    context "by status" do
      subject(:active_users) { with_user_context { OrganizationUserResource.all(filter: { status: "active" }).data } }
      subject(:inactive_users) { with_user_context { OrganizationUserResource.all(filter: { status: "inactive" }).data } }

      it "returns all active users" do
        expect(active_users.length).to eq(4)
      end

      it "returns all inactive users" do
        expect(inactive_users.length).to eq(1)
      end
    end

    context "by role_type" do
      subject(:admin_users) { with_user_context { OrganizationUserResource.all(filter: { role_type: "admin" }).data } }
      subject(:employee_users) { with_user_context { OrganizationUserResource.all(filter: { role_type: "employee" }).data } }

      it "returns only admin users" do
        expect(admin_users.length).to eq(1)
      end

      it "returns only employee users" do
        expect(employee_users.length).to eq(4)
      end
    end
  end
end
